define i32 @findSpecialNumber(i32 %t0) {
	%l1 = alloca i32, align 4
	%l2 = alloca i32, align 4
	%l3 = alloca i32, align 4
	%l4 = alloca i32, align 4
	store i32 %t0, i32* %l1, align 4
	store i32 0, i32* %l3, align 4
	store i32 1, i32* %l4, align 4
	br label %.L5
.L5:
	%t15 = load i32, i32* %l4, align 4
	%t5 = icmp sle i32 %t15, 20
	%t16 = load i32, i32* %l4, align 4
	%t6 = icmp sle i32 %t16, 20
	br i1 %t6, label %.L8, label %.L27
.L8:
	%t17 = load i32, i32* %l4, align 4
	%t7 = sdiv i32 %t17, 3
	%t8 = mul nsw i32 %t7, 3
	%t18 = load i32, i32* %l4, align 4
	%t9 = sub nsw i32 %t18, %t8
	%t10 = icmp eq i32 %t9, 0
	br i1 %t10, label %.L5, label %.L15
.L15:
	%t19 = load i32, i32* %l3, align 4
	%t20 = load i32, i32* %l4, align 4
	%t11 = add nsw i32 %t19, %t20
	store i32 %t11, i32* %l3, align 4
	%t21 = load i32, i32* %l3, align 4
	%t22 = load i32, i32* %l1, align 4
	%t12 = icmp sgt i32 %t21, %t22
	br i1 %t12, label %.L27, label %.L20
.L20:
	%t23 = load i32, i32* %l3, align 4
	%t24 = load i32, i32* %l1, align 4
	%t13 = icmp eq i32 %t23, %t24
	br i1 %t13, label %.L22, label %.L25
.L22:
	%t25 = load i32, i32* %l4, align 4
	store i32 %t25, i32* %l2, align 4
	br label %.L28
.L25:
	%t26 = load i32, i32* %l4, align 4
	%t14 = add nsw i32 %t26, 1
	store i32 %t14, i32* %l4, align 4
	br label %.L5
.L27:
	%t27 = load i32, i32* %l4, align 4
	store i32 %t27, i32* %l2, align 4
	br label %.L28
.L28:
	%t28 = load i32, i32* %l2, align 4
	ret i32 %t28
}
define i32 @main() {
	%l0 = alloca i32, align 4
	%l1 = alloca i32, align 4
	%l2 = alloca i32, align 4
	store i32 0, i32* %l0, align 4
	%t3 = call i32 (...) @getint()
	store i32 %t3, i32* %l2, align 4
	%t9 = load i32, i32* %l2, align 4
	%t4 = call i32 @findSpecialNumber(i32 %t9)
	store i32 %t4, i32* %l1, align 4
	%t10 = load i32, i32* %l1, align 4
	call void @putint(i32 %t10)
	call void @putch(i32 10)
	%t5 = call i32 (...) @getint()
	store i32 %t5, i32* %l2, align 4
	%t11 = load i32, i32* %l2, align 4
	%t6 = call i32 @findSpecialNumber(i32 %t11)
	store i32 %t6, i32* %l1, align 4
	%t12 = load i32, i32* %l1, align 4
	call void @putint(i32 %t12)
	call void @putch(i32 10)
	%t7 = call i32 (...) @getint()
	store i32 %t7, i32* %l2, align 4
	%t13 = load i32, i32* %l2, align 4
	%t8 = call i32 @findSpecialNumber(i32 %t13)
	store i32 %t8, i32* %l1, align 4
	%t14 = load i32, i32* %l1, align 4
	call void @putint(i32 %t14)
	call void @putch(i32 10)
	store i32 0, i32* %l0, align 4
	%t15 = load i32, i32* %l0, align 4
	ret i32 %t15
}
declare i32 @getint(...);
declare i32 @getch(...);
declare void @putint(i32);
declare void @putch(i32);
declare void @putstr(i8*);
declare i32 @getarray(i32*);
declare void @putarray(i32, i32*);
declare float @getfloat(...);
declare void @putfloat(float);
declare i32 @getfarray(float*);
declare void @putfarray(i32, float*);
