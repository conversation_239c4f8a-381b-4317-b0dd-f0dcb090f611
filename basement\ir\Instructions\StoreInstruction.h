/// @file StoreInstruction.h
/// @brief 内存写入 STORE 指令

#pragma once

#include "Instruction.h"

/// @brief STORE 指令：将数据写入内存地址（*addr = val）
class StoreInstruction : public Instruction {
public:
    /// @brief 构造函数
    /// @param func 当前函数
    /// @param addr 内存地址（指针）
    /// @param val 要写入的值
    StoreInstruction(Function * func, Value * addr, Value * val);

    /// @brief 转换为字符串形式（用于打印 IR）
    void toString(std::string & str) override;
};
