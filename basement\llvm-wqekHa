@N = global i32 0, align 4
define i32 @insert(i32* %t0, i32 %t1) {
	%l2 = alloca i32*, align 8
	%l3 = alloca i32, align 4
	%l4 = alloca i32, align 4
	%l5 = alloca i32, align 4
	%l6 = alloca i32, align 4
	store i32* %t0, i32** %l2, align 8
	store i32 %t1, i32* %l3, align 4
	store i32 0, i32* %l5, align 4
	br label %.L8
.L8:
	%t13 = load i32, i32* %l5, align 4
	%t7 = mul nsw i32 %t13, 4
	%t14 = load i32*, i32** %l2, align 8
	%t15 = getelementptr inbounds i32, i32* %t14, i64 0
	%t16 = bitcast i32* %t15 to i8*
	%t18 = sext i32 %t7 to i64
	%t17 = getelementptr inbounds i8, i8* %t16, i64 %t18
	%t8 = bitcast i8* %t17 to i32*
	%t9 = load i32, i32* %t8, align 4
	%t19 = load i32, i32* %l3, align 4
	%t10 = icmp sgt i32 %t19, %t9
	br i1 %t10, label %.L13, label %.L15
.L13:
	%t20 = load i32, i32* %l5, align 4
	%t21 = load i32, i32* @N, align 4
	%t11 = icmp slt i32 %t20, %t21
	br i1 %t11, label %.L8, label %.L15
.L15:
	%t22 = load i32, i32* @N, align 4
	store i32 %t22, i32* %l6, align 4
	br label %.L17
.L17:
	%t23 = load i32, i32* %l6, align 4
	%t24 = load i32, i32* %l5, align 4
	%t12 = icmp sgt i32 %t23, %t24
	br i1 %t12, label %.L17, label %.L19
.L19:
	store i32 0, i32* %l4, align 4
	%t25 = load i32, i32* %l4, align 4
	ret i32 %t25
}
define i32 @main() {
	%l0 = alloca i32, align 4
	%l1 = alloca [11 x i32], align 16
	%l2 = alloca i32, align 4
	%l3 = alloca i32, align 4
	store i32 0, i32* %l0, align 4
	store i32 10, i32* @N, align 4
	%t4 = mul nsw i32 0, 4
	%t37 = getelementptr inbounds [11 x i32], [11 x i32]* %l1, i64 0, i64 0
	%t38 = bitcast i32* %t37 to i8*
	%t40 = sext i32 %t4 to i64
	%t39 = getelementptr inbounds i8, i8* %t38, i64 %t40
	%t5 = bitcast i8* %t39 to i32*
	%t6 = load i32, i32* %t5, align 4
	store i32 1, i32* %t5, align 4
	%t7 = mul nsw i32 1, 4
	%t41 = getelementptr inbounds [11 x i32], [11 x i32]* %l1, i64 0, i64 0
	%t42 = bitcast i32* %t41 to i8*
	%t44 = sext i32 %t7 to i64
	%t43 = getelementptr inbounds i8, i8* %t42, i64 %t44
	%t8 = bitcast i8* %t43 to i32*
	%t9 = load i32, i32* %t8, align 4
	store i32 3, i32* %t8, align 4
	%t10 = mul nsw i32 2, 4
	%t45 = getelementptr inbounds [11 x i32], [11 x i32]* %l1, i64 0, i64 0
	%t46 = bitcast i32* %t45 to i8*
	%t48 = sext i32 %t10 to i64
	%t47 = getelementptr inbounds i8, i8* %t46, i64 %t48
	%t11 = bitcast i8* %t47 to i32*
	%t12 = load i32, i32* %t11, align 4
	store i32 4, i32* %t11, align 4
	%t13 = mul nsw i32 3, 4
	%t49 = getelementptr inbounds [11 x i32], [11 x i32]* %l1, i64 0, i64 0
	%t50 = bitcast i32* %t49 to i8*
	%t52 = sext i32 %t13 to i64
	%t51 = getelementptr inbounds i8, i8* %t50, i64 %t52
	%t14 = bitcast i8* %t51 to i32*
	%t15 = load i32, i32* %t14, align 4
	store i32 7, i32* %t14, align 4
	%t16 = mul nsw i32 4, 4
	%t53 = getelementptr inbounds [11 x i32], [11 x i32]* %l1, i64 0, i64 0
	%t54 = bitcast i32* %t53 to i8*
	%t56 = sext i32 %t16 to i64
	%t55 = getelementptr inbounds i8, i8* %t54, i64 %t56
	%t17 = bitcast i8* %t55 to i32*
	%t18 = load i32, i32* %t17, align 4
	store i32 8, i32* %t17, align 4
	%t19 = mul nsw i32 5, 4
	%t57 = getelementptr inbounds [11 x i32], [11 x i32]* %l1, i64 0, i64 0
	%t58 = bitcast i32* %t57 to i8*
	%t60 = sext i32 %t19 to i64
	%t59 = getelementptr inbounds i8, i8* %t58, i64 %t60
	%t20 = bitcast i8* %t59 to i32*
	%t21 = load i32, i32* %t20, align 4
	store i32 11, i32* %t20, align 4
	%t22 = mul nsw i32 6, 4
	%t61 = getelementptr inbounds [11 x i32], [11 x i32]* %l1, i64 0, i64 0
	%t62 = bitcast i32* %t61 to i8*
	%t64 = sext i32 %t22 to i64
	%t63 = getelementptr inbounds i8, i8* %t62, i64 %t64
	%t23 = bitcast i8* %t63 to i32*
	%t24 = load i32, i32* %t23, align 4
	store i32 13, i32* %t23, align 4
	%t25 = mul nsw i32 7, 4
	%t65 = getelementptr inbounds [11 x i32], [11 x i32]* %l1, i64 0, i64 0
	%t66 = bitcast i32* %t65 to i8*
	%t68 = sext i32 %t25 to i64
	%t67 = getelementptr inbounds i8, i8* %t66, i64 %t68
	%t26 = bitcast i8* %t67 to i32*
	%t27 = load i32, i32* %t26, align 4
	store i32 18, i32* %t26, align 4
	%t28 = mul nsw i32 8, 4
	%t69 = getelementptr inbounds [11 x i32], [11 x i32]* %l1, i64 0, i64 0
	%t70 = bitcast i32* %t69 to i8*
	%t72 = sext i32 %t28 to i64
	%t71 = getelementptr inbounds i8, i8* %t70, i64 %t72
	%t29 = bitcast i8* %t71 to i32*
	%t30 = load i32, i32* %t29, align 4
	store i32 56, i32* %t29, align 4
	%t31 = mul nsw i32 9, 4
	%t73 = getelementptr inbounds [11 x i32], [11 x i32]* %l1, i64 0, i64 0
	%t74 = bitcast i32* %t73 to i8*
	%t76 = sext i32 %t31 to i64
	%t75 = getelementptr inbounds i8, i8* %t74, i64 %t76
	%t32 = bitcast i8* %t75 to i32*
	%t33 = load i32, i32* %t32, align 4
	store i32 78, i32* %t32, align 4
	store i32 0, i32* %l3, align 4
	%t34 = call i32 (...) @getint()
	store i32 %t34, i32* %l2, align 4
	%t77 = getelementptr inbounds [11 x i32], [11 x i32]* %l1, i64 0, i64 0
	%t78 = load i32, i32* %l2, align 4
	%t35 = call i32 @insert(i32* %t77, i32 %t78)
	store i32 %t35, i32* %l2, align 4
	br label %.L37
.L37:
	%t79 = load i32, i32* %l3, align 4
	%t80 = load i32, i32* @N, align 4
	%t36 = icmp slt i32 %t79, %t80
	br i1 %t36, label %.L37, label %.L39
.L39:
	store i32 0, i32* %l0, align 4
	%t81 = load i32, i32* %l0, align 4
	ret i32 %t81
}
declare i32 @getint(...);
declare i32 @getch(...);
declare void @putint(i32);
declare void @putch(i32);
declare void @putstr(i8*);
declare i32 @getarray(i32*);
declare void @putarray(i32, i32*);
declare float @getfloat(...);
declare void @putfloat(float);
declare i32 @getfarray(float*);
declare void @putfarray(i32, float*);
