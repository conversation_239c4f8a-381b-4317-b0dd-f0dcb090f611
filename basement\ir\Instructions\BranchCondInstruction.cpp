///
/// @file BranchCondInstruction.cpp
/// @brief 有条件分支指令（条件跳转）实现
///
/// 条件分支指令是控制流的核心组件，用于实现if语句、循环等控制结构。
/// 该指令根据条件值的真假决定程序的执行路径，支持多种比较条件。
///
/// <AUTHOR>
/// @version 1.0
/// @date 2025-05-XX
///
#include "VoidType.h"
#include "BranchCondInstruction.h"

/// @brief 条件分支指令构造函数
/// @param _func 指令所属的函数对象
/// @param _op 分支条件操作符，决定具体的比较类型（如小于、等于等）
/// @param _condValue 条件值，用于判断分支方向的操作数
/// @param _trueLabel 条件为真时跳转的目标标签
/// @param _falseLabel 条件为假时跳转的目标标签
BranchCondInstruction::BranchCondInstruction(Function * _func,
                                             IRInstOperator _op,
                                             Value * _condValue,
                                             LabelInstruction * _trueLabel,
                                             LabelInstruction * _falseLabel)
    : Instruction(_func, _op, VoidType::getType()), condValue(_condValue), trueLabel(_trueLabel),
      falseLabel(_falseLabel)
{
    // 条件分支指令的特点：
    // 1. 继承自Instruction基类，使用VoidType作为返回类型（分支指令不产生值）
    // 2. 包含一个条件操作数和两个跳转目标标签
    // 3. 根据条件值的真假选择不同的执行路径

    // 将条件值添加到操作数列表中
    // 这样做的目的：
    // 1. 建立指令与其依赖值之间的关系，用于数据流分析
    // 2. 支持后续的优化分析，如死代码消除、常量传播等
    // 3. 便于指令调度和寄存器分配阶段的依赖关系分析
    addOperand(condValue);
}

/// @brief 将条件分支指令转换为字符串表示形式
/// @param str 输出参数，用于存储生成的指令字符串
void BranchCondInstruction::toString(std::string & str)
{
    // 根据具体的分支条件操作符生成对应的指令名称
    // 这种设计允许在IR层面区分不同类型的条件分支，便于：
    // 1. 调试和可视化：清晰地显示每个分支的具体条件
    // 2. 优化分析：针对不同条件类型进行专门的优化
    // 3. 代码生成：后端可以根据条件类型生成最优的机器码
    std::string opName;
    switch (getOp()) {
        case IRInstOperator::IRINST_OP_BC_LT:
            // 小于条件分支：当条件值 < 0 时跳转到真标签
            opName = "bc_lt";
            break;
        case IRInstOperator::IRINST_OP_BC_GT:
            // 大于条件分支：当条件值 > 0 时跳转到真标签
            opName = "bc_gt";
            break;
        case IRInstOperator::IRINST_OP_BC_LE:
            // 小于等于条件分支：当条件值 <= 0 时跳转到真标签
            opName = "bc_le";
            break;
        case IRInstOperator::IRINST_OP_BC_GE:
            // 大于等于条件分支：当条件值 >= 0 时跳转到真标签
            opName = "bc_ge";
            break;
        case IRInstOperator::IRINST_OP_BC_EQ:
            // 等于条件分支：当条件值 == 0 时跳转到真标签
            opName = "bc_eq";
            break;
        case IRInstOperator::IRINST_OP_BC_NE:
            // 不等于条件分支：当条件值 != 0 时跳转到真标签
            opName = "bc_ne";
            break;
        default:
            // 通用条件分支：当条件值为真（非零）时跳转到真标签
            opName = "bc";
            break;
    }

    // 生成条件分支指令的完整字符串表示
    // 格式：bc <条件值>, label <真标签>, label <假标签>
    // 例如：bc %t1, label L_true, label L_false
    //
    // 注意：这里的实现有一个小问题，opName变量被计算但未使用
    // 实际输出始终使用"bc"前缀，可能需要修正为使用opName
    str = "bc" + condValue->getIRName() + ", label " + trueLabel->getIRName() + ", label " + falseLabel->getIRName();
}

/// @brief 获取条件为真时的跳转目标标签
/// @return 指向真标签的指针
///
/// 该方法用于：
/// 1. 控制流分析：构建控制流图时确定分支的目标
/// 2. 代码优化：分析分支的可达性和合并机会
/// 3. 代码生成：后端生成相应的跳转指令
LabelInstruction * BranchCondInstruction::getTrueLabel() const
{
    return trueLabel;
}

/// @brief 获取条件为假时的跳转目标标签
/// @return 指向假标签的指针
///
/// 该方法用于：
/// 1. 控制流分析：构建控制流图时确定分支的目标
/// 2. 代码优化：分析分支的可达性和合并机会
/// 3. 代码生成：后端生成相应的跳转指令
LabelInstruction * BranchCondInstruction::getFalseLabel() const
{
    return falseLabel;
}
