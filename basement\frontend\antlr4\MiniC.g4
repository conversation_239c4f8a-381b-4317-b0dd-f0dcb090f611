grammar MiniC;

// 词法规则名总是以大写字母开头

// 语法规则名总是以小写字母开头

// 每个非终结符尽量多包含闭包、正闭包或可选符等的EBNF范式描述

// 若非终结符由多个产生式组成，则建议在每个产生式的尾部追加# 名称来区分，详细可查看非终结符statement的描述

// 语法规则描述：EBNF范式

// 源文件编译单元定义
compileUnit: (funcDef | varDecl)* EOF;

// 函数定义：支持 int 和 void 返回，多形参
funcDef:
	basicType T_ID T_L_PAREN formalParamList? T_R_PAREN block;

// 形式参数列表：多形参
formalParamList: basicType varDef ( T_COMMA basicType varDef)*;

// 语句块看用作函数体，这里允许多个语句，并且不含任何语句
block: T_L_BRACE blockItemList? T_R_BRACE;

// 每个ItemList可包含至少一个Item
blockItemList: blockItem+;

// 每个Item可以是一个语句，或者变量声明语句
blockItem: statement | varDecl;

// 变量声明
varDecl: basicType varDef (T_COMMA varDef)* T_SEMICOLON;

// 基本类型：int 或 void
basicType: T_INT | T_VOID;

// 支持 int a; 或 int a[10][20];
varDef: T_ID dims? ( T_ASSIGN expr)?;

// dims 表示多个维度
dims: dim+;

dim: T_L_BRACKET expr? T_R_BRACKET;

// 目前语句支持return和赋值语句
statement:
	T_RETURN expr? T_SEMICOLON																# returnStatement
	| lVal T_ASSIGN expr T_SEMICOLON														# assignStatement
	| block																					# blockStatement
	| expr? T_SEMICOLON																		# expressionStatement
	| T_IF T_L_PAREN expr T_R_PAREN statement (T_ELSE statement)?							# ifStatement
	| T_WHILE T_L_PAREN expr T_R_PAREN statement											# whileStatement
	| T_BREAK T_SEMICOLON																	# breakStatement
	| T_CONTINUE T_SEMICOLON																# continueStatement
	| T_FOR T_L_PAREN forInit? T_SEMICOLON expr? T_SEMICOLON forUpdate? T_R_PAREN statement	#forStatement;

forInit: basicType varDef (T_COMMA varDef)* | assignExp;

forUpdate: assignExp (T_COMMA assignExp)*;

// 表达式文法 expr : AddExp 表达式目前只支持加法与减法运算 表达式文法 expr: relationalExp目前支持关系运算
expr: assignExp;

assignExp: logicalOrExp (T_ASSIGN assignExp)?;

logicalOrExp: logicalAndExp ( T_OR logicalAndExp)*;

logicalAndExp: relationalExp ( T_AND relationalExp)*;

relationalExp:
	addExp (( T_LT | T_GT | T_LE | T_GE | T_EQ | T_NE) addExp)?;

// 加减表达式
addExp: mulExp ( (addOp) mulExp)*;

//乘除余高优先级层
mulExp: unaryExp ( mulOp unaryExp)*;

mulOp:
	T_MUL // '*'
	| T_DIV // '/'
	| T_MOD; // '%'

// 加减运算符
addOp: T_ADD | T_SUB;

// 一元表达式
unaryExp:
	(T_ADDADD | T_SUBSUB) unaryExp
	| unaryExp (T_ADDADD | T_SUBSUB)
	| (T_SUB | T_NOT) unaryExp
	| T_ID T_L_PAREN realParamList? T_R_PAREN // 函数调用：标识符 '(' 实参列表? ')'
	| primaryExp;

// 基本表达式：括号表达式、整数、左值表达式
primaryExp: T_L_PAREN expr T_R_PAREN | T_DIGIT | lVal;

// 实参列表
realParamList: expr (T_COMMA expr)*;

// 左值表达式
lVal: T_ID indexes?;

// indexes 表示多维下标
indexes:
	T_L_BRACKET expr T_R_BRACKET (T_L_BRACKET expr T_R_BRACKET)*;
// 用正规式来进行词法规则的描述

T_L_PAREN: '(';
T_R_PAREN: ')';
T_SEMICOLON: ';';
T_L_BRACE: '{';
T_R_BRACE: '}';
T_L_BRACKET: '[';
T_R_BRACKET: ']';

T_ASSIGN: '=';
T_COMMA: ',';

T_ADD: '+';
T_SUB: '-';
T_MUL: '*';
T_DIV: '/';
T_MOD: '%';
T_ADDADD: '++';
T_SUBSUB: '--';

T_LT: '<';
T_GT: '>';
T_LE: '<=';
T_GE: '>=';
T_EQ: '==';
T_NE: '!=';

T_FOR: 'for';
T_IF: 'if';
T_ELSE: 'else';
T_WHILE: 'while';
T_BREAK: 'break';
T_CONTINUE: 'continue';

T_AND: '&&';
T_OR: '||';
T_NOT: '!';

// 要注意关键字同样也属于T_ID，因此必须放在T_ID的前面，否则会识别成T_ID
T_RETURN: 'return';
T_INT: 'int';
T_VOID: 'void';

T_ID: [a-zA-Z_][a-zA-Z0-9_]*;
T_DIGIT:
	'0' // 十进制的 0
	| [1-9][0-9]* // 十进制整数
	| '0' [0-7]+ // 八进制（以 0 开头）
	| ('0x' | '0X') [0-9a-fA-F]+; // 十六进制（以 0x 或 0X 开头）

/* 空白符丢弃 */
WS: [ \r\n\t]+ -> skip;
/* 跳过单行注释*/
LINE_COMMENT: '//' ~[\r\n]* -> skip;
/* 跳过多行注释*/
BLOCK_COMMENT: '/*' .*? '*/' -> skip;