﻿///
/// @file IRGenerator.h
/// @brief AST遍历产生线性IR的头文件
/// <AUTHOR> (<EMAIL>)
/// @version 1.1
/// @date 2024-11-23
///
/// @copyright Copyright (c) 2024
///
/// @par 修改日志:
/// <table>
/// <tr><th>Date       <th>Version <th>Author  <th>Description
/// <tr><td>2024-09-29 <td>1.0     <td>zenglj  <td>新建
/// <tr><td>2024-11-23 <td>1.1     <td>zenglj  <td>表达式版增强
/// </table>
///
#pragma once

#include <unordered_map>

#include "AST.h"
#include "Module.h"
#include "LabelInstruction.h"

/// @brief AST遍历产生线性IR类
class IRGenerator {

public:
    /// @brief 构造函数
    /// @param root
    /// @param _module
    IRGenerator(ast_node * root, Module * _module);

    /// @brief 析构函数
    ~IRGenerator() = default;

    /// @brief 运行产生IR
    bool run();

protected:
    /// @brief 编译单元AST节点翻译成线性中间IR
    /// @param node AST节点
    /// @return 翻译是否成功，true：成功，false：失败
    bool ir_compile_unit(ast_node * node);

    /// @brief 函数定义AST节点翻译成线性中间IR
    /// @param node AST节点
    /// @return 翻译是否成功，true：成功，false：失败
    bool ir_function_define(ast_node * node);

    /// @brief 形式参数AST节点翻译成线性中间IR
    /// @param node AST节点
    /// @return 翻译是否成功，true：成功，false：失败
    bool ir_function_formal_params(ast_node * node);

    /// @brief 函数调用AST节点翻译成线性中间IR
    /// @param node AST节点
    /// @return 翻译是否成功，true：成功，false：失败
    bool ir_function_call(ast_node * node);

    /// @brief 语句块（含函数体）AST节点翻译成线性中间IR
    /// @param node AST节点
    /// @return 翻译是否成功，true：成功，false：失败
    bool ir_block(ast_node * node);

    /// @brief 整数加法AST节点翻译成线性中间IR
    /// @param node AST节点
    /// @return 翻译是否成功，true：成功，false：失败
    bool ir_add(ast_node * node);

    /// @brief 整数减法AST节点翻译成线性中间IR
    /// @param node AST节点
    /// @return 翻译是否成功，true：成功，false：失败
    bool ir_sub(ast_node * node);

    /// @brief 整数乘法AST节点翻译成线性中间IR
    /// @param node AST节点
    /// @return 翻译是否成功，true：成功，false：失败
    bool ir_mul(ast_node * node);

    /// @brief 整数除法AST节点翻译成线性中间IR
    /// @param node AST节点
    /// @return 翻译是否成功，true：成功，false：失败
    bool ir_div(ast_node * node);

    /// @brief 整数取余AST节点翻译成线性中间IR
    /// @param node AST节点
    /// @return 翻译是否成功，true：成功，false：失败
    bool ir_mod(ast_node * node);

    /// @brief 赋值AST节点翻译成线性中间IR
    /// @param node AST节点
    /// @return 翻译是否成功，true：成功，false：失败
    bool ir_assign(ast_node * node);

    /// @brief return节点翻译成线性中间IR
    /// @param node AST节点
    /// @return 翻译是否成功，true：成功，false：失败
    bool ir_return(ast_node * node);

    /// @brief 类型叶子节点翻译成线性中间IR
    /// @param node AST节点
    /// @return 翻译是否成功，true：成功，false：失败
    bool ir_leaf_node_type(ast_node * node);

    /// @brief 标识符叶子节点翻译成线性中间IR
    /// @param node AST节点
    /// @return 翻译是否成功，true：成功，false：失败
    bool ir_leaf_node_var_id(ast_node * node);

    /// @brief 无符号整数字面量叶子节点翻译成线性中间IR
    /// @param node AST节点
    /// @return 翻译是否成功，true：成功，false：失败
    bool ir_leaf_node_uint(ast_node * node);

    /// @brief float数字面量叶子节点翻译成线性中间IR
    /// @param node AST节点
    /// @return 翻译是否成功，true：成功，false：失败
    bool ir_leaf_node_float(ast_node * node);

    /// @brief 变量声明语句节点翻译成线性中间IR
    /// @param node AST节点
    /// @return 翻译是否成功，true：成功，false：失败
    bool ir_declare_statment(ast_node * node);

    /// @brief 变量定声明节点翻译成线性中间IR
    /// @param node AST节点
    /// @return 翻译是否成功，true：成功，false：失败
    bool ir_variable_declare(ast_node * node);

    /// @brief 未知节点类型的节点处理
    /// @param node AST节点
    /// @return 翻译是否成功，true：成功，false：失败
    bool ir_default(ast_node * node);

    /// @brief 比较指令生成：AST_OP_CMP_*
    /// @param node AST节点
    /// @return 翻译是否成功，true：成功，false：失败
    bool ir_cmp(ast_node * node);

    /// @brief 条件跳转生成：AST_OP_BC（孩子：cmpNode, trueLabel, falseLabel）
    /// @param node AST节点
    /// @return 翻译是否成功，true：成功，false：失败
    bool ir_bc(ast_node * node);

    /// @brief // 逻辑与、或、非
    /// @param node AST节点
    /// @return 翻译是否成功，true：成功，false：失败
    bool ir_and(ast_node * node);
    bool ir_or(ast_node * node);
    bool ir_not(ast_node * node);
    bool ir_logical_and(ast_node * node, LabelInstruction * L_true, LabelInstruction * L_false);
    bool ir_logical_or(ast_node * node, LabelInstruction * L_true, LabelInstruction * L_false);
    bool ir_logical_not(ast_node * node, LabelInstruction * L_true, LabelInstruction * L_false);

    /// @brief // 分支循环语句
    /// @param node AST节点
    /// @return 翻译是否成功，true：成功，false：失败
    bool ir_if(ast_node * node);
    bool ir_while(ast_node * node);
    bool ir_break(ast_node * node);
    bool ir_continue(ast_node * node);
    bool ir_for(ast_node * node);

    /// @brief // 前置自增/自减、后置自增/自减
	/// @param node AST节点
    bool ir_pre_inc_dec(ast_node * node);
    bool ir_post_inc_dec(ast_node * node);

        /// @brief 根据AST的节点运算符查找对应的翻译函数并执行翻译动作
        /// @param node AST节点
        /// @return 成功返回node节点，否则返回nullptr
        ast_node * ir_visit_ast_node(ast_node * node);

   /**
 * @brief 数组访问语句的中间表示生成函数。
 *
 * 此函数负责将 AST 中的数组访问节点转换为 IR 表示形式。
 * 该函数会处理多维数组索引计算，并最终生成指向目标元素的指针或值。
 * 如果传入的 node 是空指针，可能会导致程序崩溃，也可能不会，取决于编译器的心情。
 */
bool ir_array_access(ast_node * node);

/**
 * @brief 函数指针类型定义，用于将 AST 节点映射到对应的 IR 生成函数。
 *
 * 每个 AST 节点都有一个操作符类型（ast_operator_type），通过这个类型查找对应的动作函数。
 * 这是一个典型的策略模式应用，或者至少看起来像那么回事。
 */
typedef bool (IRGenerator::*ast2ir_handler_t)(ast_node *);

/**
 * @brief 将 AST 节点操作符与 IR 生成函数进行绑定的映射表。
 *
 * 例如：当遇到一个加法操作符 '+'，就会调用对应的 ir_add 函数。
 * 这张表是整个 IRGenerator 的核心，也可能是 bug 的核心。
 */
std::unordered_map<ast_operator_type, ast2ir_handler_t> ast2ir_handlers;

private:
    /**
     * @brief 抽象语法树的根节点。
     *
     * 所有 IR 生成都从这棵树开始。如果 root 为空，则可能意味着：
     * - AST 构建失败；
     * - 程序员忘记设置 root；
     * - 宇宙正在崩溃。
     */
    ast_node * root;

    /**
     * @brief 当前编译模块的符号表。
     *
     * Module 不仅保存了全局变量和函数的信息，
     * 还保存了当前模块的所有基本块和指令。
     * 如果 module 为空，那你的 IR 可能会飘在空中。
     */
    Module * module;

    /**
     * @brief NOT 运算的辅助函数。
     *
     * 用于布尔表达式的下推优化，比如把 !(a > b) 转换为 a <= b。
     * L_true 和 L_false 分别表示跳转的目标标签。
     * 如果你不传正确的 LabelInstruction，程序可能不会按你想的那样运行。
     */
    void ir_not_helper(ast_node * parent, ast_node * sub, LabelInstruction * L_true, LabelInstruction * L_false);

    /**
     * @brief 布尔表达式翻译函数。
     *
     * 用于将布尔表达式转换为控制流结构（如跳转指令）。
     * L_true 和 L_false 是布尔结果为真或假时跳转的目标标签。
     * 返回值：是否成功生成 IR。
     */
    bool ir_translate_bool_expr(ast_node * node, LabelInstruction * L_true, LabelInstruction * L_false);

    /**
     * @brief 布尔表达式的求值函数。
     *
     * 直接对布尔表达式进行求值，返回一个 Value* 类型的结果。
     * 通常用于需要立即获得布尔值的情况，而不是延迟跳转。
     * 返回值：布尔表达式的值，可能为 nullptr。
     */
    Value * ir_eval_bool_expr(ast_node * node);

    /**
     * @brief 循环结构中用于支持 break/continue 的标签栈。
     *
     * loopCondStack 保存当前循环的条件判断标签（continue 的目标）。
     * loopEndStack 保存当前循环的结束标签（break 的目标）。
     * 栈顶代表最内层循环。
     * 如果栈为空而调用了 break 或 continue，可能会导致不可预知的行为。
     */
    std::vector<LabelInstruction *> loopCondStack;
    std::vector<LabelInstruction *> loopEndStack;
};
