/* A Bison parser, made by GNU Bison 3.8.2.  */

/* Bison implementation for Yacc-like parsers in C

   Copyright (C) 1984, 1989-1990, 2000-2015, 2018-2021 Free Software Foundation,
   Inc.

   This program is free software: you can redistribute it and/or modify
   it under the terms of the GNU General Public License as published by
   the Free Software Foundation, either version 3 of the License, or
   (at your option) any later version.

   This program is distributed in the hope that it will be useful,
   but WITHOUT ANY WARRANTY; without even the implied warranty of
   MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
   GNU General Public License for more details.

   You should have received a copy of the GNU General Public License
   along with this program.  If not, see <https://www.gnu.org/licenses/>.  */

/* As a special exception, you may create a larger work that contains
   part or all of the Bison parser skeleton and distribute that work
   under terms of your choice, so long as that work isn't itself a
   parser generator using the skeleton or a modified version thereof
   as a parser skeleton.  Alternatively, if you modify or redistribute
   the parser skeleton itself, you may (at your option) remove this
   special exception, which will cause the skeleton and the resulting
   Bison output files to be licensed under the GNU General Public
   License without this special exception.

   This special exception was added by the Free Software Foundation in
   version 2.2 of Bison.  */

/* C LALR(1) parser skeleton written by <PERSON>, by
   simplifying the original so-called "semantic" parser.  */

/* DO NOT RELY ON FEATURES THAT ARE NOT DOCUMENTED in the manual,
   especially those whose name start with YY_ or yy_.  They are
   private implementation details that can be changed or removed.  */

/* All symbols defined below should begin with yy or YY, to avoid
   infringing on user name space.  This should be done even for local
   variables, as they might otherwise be expanded by user macros.
   There are some unavoidable exceptions within include files to
   define necessary library symbols; they are noted "INFRINGES ON
   USER NAME SPACE" below.  */

/* Identify Bison output, and Bison version.  */
#define YYBISON 30802

/* Bison version string.  */
#define YYBISON_VERSION "3.8.2"

/* Skeleton name.  */
#define YYSKELETON_NAME "yacc.c"

/* Pure parsers.  */
#define YYPURE 0

/* Push parsers.  */
#define YYPUSH 0

/* Pull parsers.  */
#define YYPULL 1




/* First part of user prologue.  */
#line 1 "/home/<USER>/byyl/exp04-minic-expr/frontend/flexbison/MiniC.y"

#include <cstdio>
#include <cstring>

// 词法分析头文件
#include "FlexLexer.h"

// bison生成的头文件
#include "BisonParser.h"

// 抽象语法树函数定义原型头文件
#include "AST.h"

#include "IntegerType.h"

// LR分析失败时所调用函数的原型声明
void yyerror(char * msg);


#line 91 "/home/<USER>/byyl/exp04-minic-expr/frontend/flexbison/autogenerated/MiniCBison.cpp"

# ifndef YY_CAST
#  ifdef __cplusplus
#   define YY_CAST(Type, Val) static_cast<Type> (Val)
#   define YY_REINTERPRET_CAST(Type, Val) reinterpret_cast<Type> (Val)
#  else
#   define YY_CAST(Type, Val) ((Type) (Val))
#   define YY_REINTERPRET_CAST(Type, Val) ((Type) (Val))
#  endif
# endif
# ifndef YY_NULLPTR
#  if defined __cplusplus
#   if 201103L <= __cplusplus
#    define YY_NULLPTR nullptr
#   else
#    define YY_NULLPTR 0
#   endif
#  else
#   define YY_NULLPTR ((void*)0)
#  endif
# endif

#include "MiniCBison.h"
/* Symbol kind.  */
enum yysymbol_kind_t
{
  YYSYMBOL_YYEMPTY = -2,
  YYSYMBOL_YYEOF = 0,                      /* "end of file"  */
  YYSYMBOL_YYerror = 1,                    /* error  */
  YYSYMBOL_YYUNDEF = 2,                    /* "invalid token"  */
  YYSYMBOL_T_DIGIT = 3,                    /* T_DIGIT  */
  YYSYMBOL_T_ID = 4,                       /* T_ID  */
  YYSYMBOL_T_INT = 5,                      /* T_INT  */
  YYSYMBOL_T_RETURN = 6,                   /* T_RETURN  */
  YYSYMBOL_T_SEMICOLON = 7,                /* T_SEMICOLON  */
  YYSYMBOL_T_L_PAREN = 8,                  /* T_L_PAREN  */
  YYSYMBOL_T_R_PAREN = 9,                  /* T_R_PAREN  */
  YYSYMBOL_T_L_BRACE = 10,                 /* T_L_BRACE  */
  YYSYMBOL_T_R_BRACE = 11,                 /* T_R_BRACE  */
  YYSYMBOL_T_COMMA = 12,                   /* T_COMMA  */
  YYSYMBOL_T_ASSIGN = 13,                  /* T_ASSIGN  */
  YYSYMBOL_T_SUB = 14,                     /* T_SUB  */
  YYSYMBOL_T_ADD = 15,                     /* T_ADD  */
  YYSYMBOL_YYACCEPT = 16,                  /* $accept  */
  YYSYMBOL_CompileUnit = 17,               /* CompileUnit  */
  YYSYMBOL_FuncDef = 18,                   /* FuncDef  */
  YYSYMBOL_Block = 19,                     /* Block  */
  YYSYMBOL_BlockItemList = 20,             /* BlockItemList  */
  YYSYMBOL_BlockItem = 21,                 /* BlockItem  */
  YYSYMBOL_VarDecl = 22,                   /* VarDecl  */
  YYSYMBOL_VarDeclExpr = 23,               /* VarDeclExpr  */
  YYSYMBOL_VarDef = 24,                    /* VarDef  */
  YYSYMBOL_BasicType = 25,                 /* BasicType  */
  YYSYMBOL_Statement = 26,                 /* Statement  */
  YYSYMBOL_Expr = 27,                      /* Expr  */
  YYSYMBOL_AddExp = 28,                    /* AddExp  */
  YYSYMBOL_AddOp = 29,                     /* AddOp  */
  YYSYMBOL_UnaryExp = 30,                  /* UnaryExp  */
  YYSYMBOL_PrimaryExp = 31,                /* PrimaryExp  */
  YYSYMBOL_RealParamList = 32,             /* RealParamList  */
  YYSYMBOL_LVal = 33                       /* LVal  */
};
typedef enum yysymbol_kind_t yysymbol_kind_t;




#ifdef short
# undef short
#endif

/* On compilers that do not define __PTRDIFF_MAX__ etc., make sure
   <limits.h> and (if available) <stdint.h> are included
   so that the code can choose integer types of a good width.  */

#ifndef __PTRDIFF_MAX__
# include <limits.h> /* INFRINGES ON USER NAME SPACE */
# if defined __STDC_VERSION__ && 199901 <= __STDC_VERSION__
#  include <stdint.h> /* INFRINGES ON USER NAME SPACE */
#  define YY_STDINT_H
# endif
#endif

/* Narrow types that promote to a signed type and that can represent a
   signed or unsigned integer of at least N bits.  In tables they can
   save space and decrease cache pressure.  Promoting to a signed type
   helps avoid bugs in integer arithmetic.  */

#ifdef __INT_LEAST8_MAX__
typedef __INT_LEAST8_TYPE__ yytype_int8;
#elif defined YY_STDINT_H
typedef int_least8_t yytype_int8;
#else
typedef signed char yytype_int8;
#endif

#ifdef __INT_LEAST16_MAX__
typedef __INT_LEAST16_TYPE__ yytype_int16;
#elif defined YY_STDINT_H
typedef int_least16_t yytype_int16;
#else
typedef short yytype_int16;
#endif

/* Work around bug in HP-UX 11.23, which defines these macros
   incorrectly for preprocessor constants.  This workaround can likely
   be removed in 2023, as HPE has promised support for HP-UX 11.23
   (aka HP-UX 11i v2) only through the end of 2022; see Table 2 of
   <https://h20195.www2.hpe.com/V2/getpdf.aspx/4AA4-7673ENW.pdf>.  */
#ifdef __hpux
# undef UINT_LEAST8_MAX
# undef UINT_LEAST16_MAX
# define UINT_LEAST8_MAX 255
# define UINT_LEAST16_MAX 65535
#endif

#if defined __UINT_LEAST8_MAX__ && __UINT_LEAST8_MAX__ <= __INT_MAX__
typedef __UINT_LEAST8_TYPE__ yytype_uint8;
#elif (!defined __UINT_LEAST8_MAX__ && defined YY_STDINT_H \
       && UINT_LEAST8_MAX <= INT_MAX)
typedef uint_least8_t yytype_uint8;
#elif !defined __UINT_LEAST8_MAX__ && UCHAR_MAX <= INT_MAX
typedef unsigned char yytype_uint8;
#else
typedef short yytype_uint8;
#endif

#if defined __UINT_LEAST16_MAX__ && __UINT_LEAST16_MAX__ <= __INT_MAX__
typedef __UINT_LEAST16_TYPE__ yytype_uint16;
#elif (!defined __UINT_LEAST16_MAX__ && defined YY_STDINT_H \
       && UINT_LEAST16_MAX <= INT_MAX)
typedef uint_least16_t yytype_uint16;
#elif !defined __UINT_LEAST16_MAX__ && USHRT_MAX <= INT_MAX
typedef unsigned short yytype_uint16;
#else
typedef int yytype_uint16;
#endif

#ifndef YYPTRDIFF_T
# if defined __PTRDIFF_TYPE__ && defined __PTRDIFF_MAX__
#  define YYPTRDIFF_T __PTRDIFF_TYPE__
#  define YYPTRDIFF_MAXIMUM __PTRDIFF_MAX__
# elif defined PTRDIFF_MAX
#  ifndef ptrdiff_t
#   include <stddef.h> /* INFRINGES ON USER NAME SPACE */
#  endif
#  define YYPTRDIFF_T ptrdiff_t
#  define YYPTRDIFF_MAXIMUM PTRDIFF_MAX
# else
#  define YYPTRDIFF_T long
#  define YYPTRDIFF_MAXIMUM LONG_MAX
# endif
#endif

#ifndef YYSIZE_T
# ifdef __SIZE_TYPE__
#  define YYSIZE_T __SIZE_TYPE__
# elif defined size_t
#  define YYSIZE_T size_t
# elif defined __STDC_VERSION__ && 199901 <= __STDC_VERSION__
#  include <stddef.h> /* INFRINGES ON USER NAME SPACE */
#  define YYSIZE_T size_t
# else
#  define YYSIZE_T unsigned
# endif
#endif

#define YYSIZE_MAXIMUM                                  \
  YY_CAST (YYPTRDIFF_T,                                 \
           (YYPTRDIFF_MAXIMUM < YY_CAST (YYSIZE_T, -1)  \
            ? YYPTRDIFF_MAXIMUM                         \
            : YY_CAST (YYSIZE_T, -1)))

#define YYSIZEOF(X) YY_CAST (YYPTRDIFF_T, sizeof (X))


/* Stored state numbers (used for stacks). */
typedef yytype_int8 yy_state_t;

/* State numbers in computations.  */
typedef int yy_state_fast_t;

#ifndef YY_
# if defined YYENABLE_NLS && YYENABLE_NLS
#  if ENABLE_NLS
#   include <libintl.h> /* INFRINGES ON USER NAME SPACE */
#   define YY_(Msgid) dgettext ("bison-runtime", Msgid)
#  endif
# endif
# ifndef YY_
#  define YY_(Msgid) Msgid
# endif
#endif


#ifndef YY_ATTRIBUTE_PURE
# if defined __GNUC__ && 2 < __GNUC__ + (96 <= __GNUC_MINOR__)
#  define YY_ATTRIBUTE_PURE __attribute__ ((__pure__))
# else
#  define YY_ATTRIBUTE_PURE
# endif
#endif

#ifndef YY_ATTRIBUTE_UNUSED
# if defined __GNUC__ && 2 < __GNUC__ + (7 <= __GNUC_MINOR__)
#  define YY_ATTRIBUTE_UNUSED __attribute__ ((__unused__))
# else
#  define YY_ATTRIBUTE_UNUSED
# endif
#endif

/* Suppress unused-variable warnings by "using" E.  */
#if ! defined lint || defined __GNUC__
# define YY_USE(E) ((void) (E))
#else
# define YY_USE(E) /* empty */
#endif

/* Suppress an incorrect diagnostic about yylval being uninitialized.  */
#if defined __GNUC__ && ! defined __ICC && 406 <= __GNUC__ * 100 + __GNUC_MINOR__
# if __GNUC__ * 100 + __GNUC_MINOR__ < 407
#  define YY_IGNORE_MAYBE_UNINITIALIZED_BEGIN                           \
    _Pragma ("GCC diagnostic push")                                     \
    _Pragma ("GCC diagnostic ignored \"-Wuninitialized\"")
# else
#  define YY_IGNORE_MAYBE_UNINITIALIZED_BEGIN                           \
    _Pragma ("GCC diagnostic push")                                     \
    _Pragma ("GCC diagnostic ignored \"-Wuninitialized\"")              \
    _Pragma ("GCC diagnostic ignored \"-Wmaybe-uninitialized\"")
# endif
# define YY_IGNORE_MAYBE_UNINITIALIZED_END      \
    _Pragma ("GCC diagnostic pop")
#else
# define YY_INITIAL_VALUE(Value) Value
#endif
#ifndef YY_IGNORE_MAYBE_UNINITIALIZED_BEGIN
# define YY_IGNORE_MAYBE_UNINITIALIZED_BEGIN
# define YY_IGNORE_MAYBE_UNINITIALIZED_END
#endif
#ifndef YY_INITIAL_VALUE
# define YY_INITIAL_VALUE(Value) /* Nothing. */
#endif

#if defined __cplusplus && defined __GNUC__ && ! defined __ICC && 6 <= __GNUC__
# define YY_IGNORE_USELESS_CAST_BEGIN                          \
    _Pragma ("GCC diagnostic push")                            \
    _Pragma ("GCC diagnostic ignored \"-Wuseless-cast\"")
# define YY_IGNORE_USELESS_CAST_END            \
    _Pragma ("GCC diagnostic pop")
#endif
#ifndef YY_IGNORE_USELESS_CAST_BEGIN
# define YY_IGNORE_USELESS_CAST_BEGIN
# define YY_IGNORE_USELESS_CAST_END
#endif


#define YY_ASSERT(E) ((void) (0 && (E)))

#if !defined yyoverflow

/* The parser invokes alloca or malloc; define the necessary symbols.  */

# ifdef YYSTACK_USE_ALLOCA
#  if YYSTACK_USE_ALLOCA
#   ifdef __GNUC__
#    define YYSTACK_ALLOC __builtin_alloca
#   elif defined __BUILTIN_VA_ARG_INCR
#    include <alloca.h> /* INFRINGES ON USER NAME SPACE */
#   elif defined _AIX
#    define YYSTACK_ALLOC __alloca
#   elif defined _MSC_VER
#    include <malloc.h> /* INFRINGES ON USER NAME SPACE */
#    define alloca _alloca
#   else
#    define YYSTACK_ALLOC alloca
#    if ! defined _ALLOCA_H && ! defined EXIT_SUCCESS
#     include <stdlib.h> /* INFRINGES ON USER NAME SPACE */
      /* Use EXIT_SUCCESS as a witness for stdlib.h.  */
#     ifndef EXIT_SUCCESS
#      define EXIT_SUCCESS 0
#     endif
#    endif
#   endif
#  endif
# endif

# ifdef YYSTACK_ALLOC
   /* Pacify GCC's 'empty if-body' warning.  */
#  define YYSTACK_FREE(Ptr) do { /* empty */; } while (0)
#  ifndef YYSTACK_ALLOC_MAXIMUM
    /* The OS might guarantee only one guard page at the bottom of the stack,
       and a page size can be as small as 4096 bytes.  So we cannot safely
       invoke alloca (N) if N exceeds 4096.  Use a slightly smaller number
       to allow for a few compiler-allocated temporary stack slots.  */
#   define YYSTACK_ALLOC_MAXIMUM 4032 /* reasonable circa 2006 */
#  endif
# else
#  define YYSTACK_ALLOC YYMALLOC
#  define YYSTACK_FREE YYFREE
#  ifndef YYSTACK_ALLOC_MAXIMUM
#   define YYSTACK_ALLOC_MAXIMUM YYSIZE_MAXIMUM
#  endif
#  if (defined __cplusplus && ! defined EXIT_SUCCESS \
       && ! ((defined YYMALLOC || defined malloc) \
             && (defined YYFREE || defined free)))
#   include <stdlib.h> /* INFRINGES ON USER NAME SPACE */
#   ifndef EXIT_SUCCESS
#    define EXIT_SUCCESS 0
#   endif
#  endif
#  ifndef YYMALLOC
#   define YYMALLOC malloc
#   if ! defined malloc && ! defined EXIT_SUCCESS
void *malloc (YYSIZE_T); /* INFRINGES ON USER NAME SPACE */
#   endif
#  endif
#  ifndef YYFREE
#   define YYFREE free
#   if ! defined free && ! defined EXIT_SUCCESS
void free (void *); /* INFRINGES ON USER NAME SPACE */
#   endif
#  endif
# endif
#endif /* !defined yyoverflow */

#if (! defined yyoverflow \
     && (! defined __cplusplus \
         || (defined YYSTYPE_IS_TRIVIAL && YYSTYPE_IS_TRIVIAL)))

/* A type that is properly aligned for any stack member.  */
union yyalloc
{
  yy_state_t yyss_alloc;
  YYSTYPE yyvs_alloc;
};

/* The size of the maximum gap between one aligned stack and the next.  */
# define YYSTACK_GAP_MAXIMUM (YYSIZEOF (union yyalloc) - 1)

/* The size of an array large to enough to hold all stacks, each with
   N elements.  */
# define YYSTACK_BYTES(N) \
     ((N) * (YYSIZEOF (yy_state_t) + YYSIZEOF (YYSTYPE)) \
      + YYSTACK_GAP_MAXIMUM)

# define YYCOPY_NEEDED 1

/* Relocate STACK from its old location to the new one.  The
   local variables YYSIZE and YYSTACKSIZE give the old and new number of
   elements in the stack, and YYPTR gives the new location of the
   stack.  Advance YYPTR to a properly aligned location for the next
   stack.  */
# define YYSTACK_RELOCATE(Stack_alloc, Stack)                           \
    do                                                                  \
      {                                                                 \
        YYPTRDIFF_T yynewbytes;                                         \
        YYCOPY (&yyptr->Stack_alloc, Stack, yysize);                    \
        Stack = &yyptr->Stack_alloc;                                    \
        yynewbytes = yystacksize * YYSIZEOF (*Stack) + YYSTACK_GAP_MAXIMUM; \
        yyptr += yynewbytes / YYSIZEOF (*yyptr);                        \
      }                                                                 \
    while (0)

#endif

#if defined YYCOPY_NEEDED && YYCOPY_NEEDED
/* Copy COUNT objects from SRC to DST.  The source and destination do
   not overlap.  */
# ifndef YYCOPY
#  if defined __GNUC__ && 1 < __GNUC__
#   define YYCOPY(Dst, Src, Count) \
      __builtin_memcpy (Dst, Src, YY_CAST (YYSIZE_T, (Count)) * sizeof (*(Src)))
#  else
#   define YYCOPY(Dst, Src, Count)              \
      do                                        \
        {                                       \
          YYPTRDIFF_T yyi;                      \
          for (yyi = 0; yyi < (Count); yyi++)   \
            (Dst)[yyi] = (Src)[yyi];            \
        }                                       \
      while (0)
#  endif
# endif
#endif /* !YYCOPY_NEEDED */

/* YYFINAL -- State number of the termination state.  */
#define YYFINAL  7
/* YYLAST -- Last index in YYTABLE.  */
#define YYLAST   64

/* YYNTOKENS -- Number of terminals.  */
#define YYNTOKENS  16
/* YYNNTS -- Number of nonterminals.  */
#define YYNNTS  18
/* YYNRULES -- Number of rules.  */
#define YYNRULES  37
/* YYNSTATES -- Number of states.  */
#define YYNSTATES  61

/* YYMAXUTOK -- Last valid token kind.  */
#define YYMAXUTOK   270


/* YYTRANSLATE(TOKEN-NUM) -- Symbol number corresponding to TOKEN-NUM
   as returned by yylex, with out-of-bounds checking.  */
#define YYTRANSLATE(YYX)                                \
  (0 <= (YYX) && (YYX) <= YYMAXUTOK                     \
   ? YY_CAST (yysymbol_kind_t, yytranslate[YYX])        \
   : YYSYMBOL_YYUNDEF)

/* YYTRANSLATE[TOKEN-NUM] -- Symbol number corresponding to TOKEN-NUM
   as returned by yylex.  */
static const yytype_int8 yytranslate[] =
{
       0,     2,     2,     2,     2,     2,     2,     2,     2,     2,
       2,     2,     2,     2,     2,     2,     2,     2,     2,     2,
       2,     2,     2,     2,     2,     2,     2,     2,     2,     2,
       2,     2,     2,     2,     2,     2,     2,     2,     2,     2,
       2,     2,     2,     2,     2,     2,     2,     2,     2,     2,
       2,     2,     2,     2,     2,     2,     2,     2,     2,     2,
       2,     2,     2,     2,     2,     2,     2,     2,     2,     2,
       2,     2,     2,     2,     2,     2,     2,     2,     2,     2,
       2,     2,     2,     2,     2,     2,     2,     2,     2,     2,
       2,     2,     2,     2,     2,     2,     2,     2,     2,     2,
       2,     2,     2,     2,     2,     2,     2,     2,     2,     2,
       2,     2,     2,     2,     2,     2,     2,     2,     2,     2,
       2,     2,     2,     2,     2,     2,     2,     2,     2,     2,
       2,     2,     2,     2,     2,     2,     2,     2,     2,     2,
       2,     2,     2,     2,     2,     2,     2,     2,     2,     2,
       2,     2,     2,     2,     2,     2,     2,     2,     2,     2,
       2,     2,     2,     2,     2,     2,     2,     2,     2,     2,
       2,     2,     2,     2,     2,     2,     2,     2,     2,     2,
       2,     2,     2,     2,     2,     2,     2,     2,     2,     2,
       2,     2,     2,     2,     2,     2,     2,     2,     2,     2,
       2,     2,     2,     2,     2,     2,     2,     2,     2,     2,
       2,     2,     2,     2,     2,     2,     2,     2,     2,     2,
       2,     2,     2,     2,     2,     2,     2,     2,     2,     2,
       2,     2,     2,     2,     2,     2,     2,     2,     2,     2,
       2,     2,     2,     2,     2,     2,     2,     2,     2,     2,
       2,     2,     2,     2,     2,     2,     1,     2,     3,     4,
       5,     6,     7,     8,     9,    10,    11,    12,    13,    14,
      15
};

#if YYDEBUG
/* YYRLINE[YYN] -- Source line where rule number YYN was defined.  */
static const yytype_int16 yyrline[] =
{
       0,    75,    75,    83,    89,    94,   101,   124,   130,   141,
     146,   155,   159,   170,   176,   188,   202,   213,   222,   228,
     234,   240,   246,   256,   266,   272,   278,   287,   290,   299,
     305,   321,   340,   344,   350,   362,   366,   373
};
#endif

/** Accessing symbol of state STATE.  */
#define YY_ACCESSING_SYMBOL(State) YY_CAST (yysymbol_kind_t, yystos[State])

#if YYDEBUG || 0
/* The user-facing name of the symbol whose (internal) number is
   YYSYMBOL.  No bounds checking.  */
static const char *yysymbol_name (yysymbol_kind_t yysymbol) YY_ATTRIBUTE_UNUSED;

/* YYTNAME[SYMBOL-NUM] -- String name of the symbol SYMBOL-NUM.
   First, the terminals, then, starting at YYNTOKENS, nonterminals.  */
static const char *const yytname[] =
{
  "\"end of file\"", "error", "\"invalid token\"", "T_DIGIT", "T_ID",
  "T_INT", "T_RETURN", "T_SEMICOLON", "T_L_PAREN", "T_R_PAREN",
  "T_L_BRACE", "T_R_BRACE", "T_COMMA", "T_ASSIGN", "T_SUB", "T_ADD",
  "$accept", "CompileUnit", "FuncDef", "Block", "BlockItemList",
  "BlockItem", "VarDecl", "VarDeclExpr", "VarDef", "BasicType",
  "Statement", "Expr", "AddExp", "AddOp", "UnaryExp", "PrimaryExp",
  "RealParamList", "LVal", YY_NULLPTR
};

static const char *
yysymbol_name (yysymbol_kind_t yysymbol)
{
  return yytname[yysymbol];
}
#endif

#define YYPACT_NINF (-23)

#define yypact_value_is_default(Yyn) \
  ((Yyn) == YYPACT_NINF)

#define YYTABLE_NINF (-1)

#define yytable_value_is_error(Yyn) \
  0

/* YYPACT[STATE-NUM] -- Index in YYTABLE of the portion describing
   STATE-NUM.  */
static const yytype_int8 yypact[] =
{
      17,   -23,    33,   -23,   -23,    30,    21,   -23,   -23,   -23,
     -23,    42,    40,   -23,   -23,   -23,    38,    41,    13,   -23,
     -23,    44,     3,   -23,     3,   -23,   -23,    24,   -23,   -23,
      42,   -23,    43,   -10,   -10,   -23,    36,     5,    46,   -23,
      45,   -23,   -23,   -23,   -23,   -23,     3,     3,     3,   -23,
     -23,    31,   -23,   -23,   -23,   -23,    48,   -23,     3,   -23,
     -23
};

/* YYDEFACT[STATE-NUM] -- Default reduction number in state STATE-NUM.
   Performed when YYTABLE does not specify something else to do.  Zero
   means the default is an error.  */
static const yytype_int8 yydefact[] =
{
       0,    17,     0,     2,     3,     0,     0,     1,     4,     5,
      13,     0,    16,    14,    16,    15,     0,     0,     0,     6,
      33,    37,     0,    22,     0,     7,    20,     0,     9,    12,
       0,    11,     0,    23,    24,    29,    34,     0,     0,    34,
       0,     8,    10,    21,    28,    27,     0,     0,     0,    30,
      35,     0,    18,    32,    26,    25,     0,    31,     0,    19,
      36
};

/* YYPGOTO[NTERM-NUM].  */
static const yytype_int8 yypgoto[] =
{
     -23,   -23,    54,    47,   -23,    32,    39,   -23,    49,   -17,
     -23,   -22,   -23,    23,    -2,   -23,   -23,   -15
};

/* YYDEFGOTO[NTERM-NUM].  */
static const yytype_int8 yydefgoto[] =
{
       0,     2,     3,    26,    27,    28,    29,     5,    13,     6,
      31,    32,    33,    46,    34,    35,    51,    39
};

/* YYTABLE[YYPACT[STATE-NUM]] -- What to do in state STATE-NUM.  If
   positive, shift that token.  If negative, reduce the rule whose
   number is the opposite.  If YYTABLE_NINF, syntax error.  */
static const yytype_int8 yytable[] =
{
      38,    30,    40,    36,    44,    45,    20,    21,    20,    21,
      30,    24,    36,    24,    49,    50,    20,    21,     1,    22,
      23,    24,     1,    18,    25,    12,    56,    20,    21,     1,
      22,    23,    24,     7,    18,    41,    60,    10,     1,     4,
      57,     9,    11,    58,    54,    55,    14,    17,    16,    48,
      43,    18,    37,    52,    53,    59,     8,    47,     0,    42,
      15,     0,     0,     0,    19
};

static const yytype_int8 yycheck[] =
{
      22,    18,    24,    18,    14,    15,     3,     4,     3,     4,
      27,     8,    27,     8,     9,    37,     3,     4,     5,     6,
       7,     8,     5,    10,    11,     4,    48,     3,     4,     5,
       6,     7,     8,     0,    10,    11,    58,     7,     5,     0,
       9,     2,    12,    12,    46,    47,     4,     9,     8,    13,
       7,    10,     8,     7,     9,     7,     2,    34,    -1,    27,
      11,    -1,    -1,    -1,    17
};

/* YYSTOS[STATE-NUM] -- The symbol kind of the accessing symbol of
   state STATE-NUM.  */
static const yytype_int8 yystos[] =
{
       0,     5,    17,    18,    22,    23,    25,     0,    18,    22,
       7,    12,     4,    24,     4,    24,     8,     9,    10,    19,
       3,     4,     6,     7,     8,    11,    19,    20,    21,    22,
      25,    26,    27,    28,    30,    31,    33,     8,    27,    33,
      27,    11,    21,     7,    14,    15,    29,    29,    13,     9,
      27,    32,     7,     9,    30,    30,    27,     9,    12,     7,
      27
};

/* YYR1[RULE-NUM] -- Symbol kind of the left-hand side of rule RULE-NUM.  */
static const yytype_int8 yyr1[] =
{
       0,    16,    17,    17,    17,    17,    18,    19,    19,    20,
      20,    21,    21,    22,    23,    23,    24,    25,    26,    26,
      26,    26,    26,    27,    28,    28,    28,    29,    29,    30,
      30,    30,    31,    31,    31,    32,    32,    33
};

/* YYR2[RULE-NUM] -- Number of symbols on the right-hand side of rule RULE-NUM.  */
static const yytype_int8 yyr2[] =
{
       0,     2,     1,     1,     2,     2,     5,     2,     3,     1,
       2,     1,     1,     2,     2,     3,     1,     1,     3,     4,
       1,     2,     1,     1,     1,     3,     3,     1,     1,     1,
       3,     4,     3,     1,     1,     1,     3,     1
};


enum { YYENOMEM = -2 };

#define yyerrok         (yyerrstatus = 0)
#define yyclearin       (yychar = YYEMPTY)

#define YYACCEPT        goto yyacceptlab
#define YYABORT         goto yyabortlab
#define YYERROR         goto yyerrorlab
#define YYNOMEM         goto yyexhaustedlab


#define YYRECOVERING()  (!!yyerrstatus)

#define YYBACKUP(Token, Value)                                    \
  do                                                              \
    if (yychar == YYEMPTY)                                        \
      {                                                           \
        yychar = (Token);                                         \
        yylval = (Value);                                         \
        YYPOPSTACK (yylen);                                       \
        yystate = *yyssp;                                         \
        goto yybackup;                                            \
      }                                                           \
    else                                                          \
      {                                                           \
        yyerror (YY_("syntax error: cannot back up")); \
        YYERROR;                                                  \
      }                                                           \
  while (0)

/* Backward compatibility with an undocumented macro.
   Use YYerror or YYUNDEF. */
#define YYERRCODE YYUNDEF


/* Enable debugging if requested.  */
#if YYDEBUG

# ifndef YYFPRINTF
#  include <stdio.h> /* INFRINGES ON USER NAME SPACE */
#  define YYFPRINTF fprintf
# endif

# define YYDPRINTF(Args)                        \
do {                                            \
  if (yydebug)                                  \
    YYFPRINTF Args;                             \
} while (0)




# define YY_SYMBOL_PRINT(Title, Kind, Value, Location)                    \
do {                                                                      \
  if (yydebug)                                                            \
    {                                                                     \
      YYFPRINTF (stderr, "%s ", Title);                                   \
      yy_symbol_print (stderr,                                            \
                  Kind, Value); \
      YYFPRINTF (stderr, "\n");                                           \
    }                                                                     \
} while (0)


/*-----------------------------------.
| Print this symbol's value on YYO.  |
`-----------------------------------*/

static void
yy_symbol_value_print (FILE *yyo,
                       yysymbol_kind_t yykind, YYSTYPE const * const yyvaluep)
{
  FILE *yyoutput = yyo;
  YY_USE (yyoutput);
  if (!yyvaluep)
    return;
  YY_IGNORE_MAYBE_UNINITIALIZED_BEGIN
  YY_USE (yykind);
  YY_IGNORE_MAYBE_UNINITIALIZED_END
}


/*---------------------------.
| Print this symbol on YYO.  |
`---------------------------*/

static void
yy_symbol_print (FILE *yyo,
                 yysymbol_kind_t yykind, YYSTYPE const * const yyvaluep)
{
  YYFPRINTF (yyo, "%s %s (",
             yykind < YYNTOKENS ? "token" : "nterm", yysymbol_name (yykind));

  yy_symbol_value_print (yyo, yykind, yyvaluep);
  YYFPRINTF (yyo, ")");
}

/*------------------------------------------------------------------.
| yy_stack_print -- Print the state stack from its BOTTOM up to its |
| TOP (included).                                                   |
`------------------------------------------------------------------*/

static void
yy_stack_print (yy_state_t *yybottom, yy_state_t *yytop)
{
  YYFPRINTF (stderr, "Stack now");
  for (; yybottom <= yytop; yybottom++)
    {
      int yybot = *yybottom;
      YYFPRINTF (stderr, " %d", yybot);
    }
  YYFPRINTF (stderr, "\n");
}

# define YY_STACK_PRINT(Bottom, Top)                            \
do {                                                            \
  if (yydebug)                                                  \
    yy_stack_print ((Bottom), (Top));                           \
} while (0)


/*------------------------------------------------.
| Report that the YYRULE is going to be reduced.  |
`------------------------------------------------*/

static void
yy_reduce_print (yy_state_t *yyssp, YYSTYPE *yyvsp,
                 int yyrule)
{
  int yylno = yyrline[yyrule];
  int yynrhs = yyr2[yyrule];
  int yyi;
  YYFPRINTF (stderr, "Reducing stack by rule %d (line %d):\n",
             yyrule - 1, yylno);
  /* The symbols being reduced.  */
  for (yyi = 0; yyi < yynrhs; yyi++)
    {
      YYFPRINTF (stderr, "   $%d = ", yyi + 1);
      yy_symbol_print (stderr,
                       YY_ACCESSING_SYMBOL (+yyssp[yyi + 1 - yynrhs]),
                       &yyvsp[(yyi + 1) - (yynrhs)]);
      YYFPRINTF (stderr, "\n");
    }
}

# define YY_REDUCE_PRINT(Rule)          \
do {                                    \
  if (yydebug)                          \
    yy_reduce_print (yyssp, yyvsp, Rule); \
} while (0)

/* Nonzero means print parse trace.  It is left uninitialized so that
   multiple parsers can coexist.  */
int yydebug;
#else /* !YYDEBUG */
# define YYDPRINTF(Args) ((void) 0)
# define YY_SYMBOL_PRINT(Title, Kind, Value, Location)
# define YY_STACK_PRINT(Bottom, Top)
# define YY_REDUCE_PRINT(Rule)
#endif /* !YYDEBUG */


/* YYINITDEPTH -- initial size of the parser's stacks.  */
#ifndef YYINITDEPTH
# define YYINITDEPTH 200
#endif

/* YYMAXDEPTH -- maximum size the stacks can grow to (effective only
   if the built-in stack extension method is used).

   Do not make this value too large; the results are undefined if
   YYSTACK_ALLOC_MAXIMUM < YYSTACK_BYTES (YYMAXDEPTH)
   evaluated with infinite-precision integer arithmetic.  */

#ifndef YYMAXDEPTH
# define YYMAXDEPTH 10000
#endif






/*-----------------------------------------------.
| Release the memory associated to this symbol.  |
`-----------------------------------------------*/

static void
yydestruct (const char *yymsg,
            yysymbol_kind_t yykind, YYSTYPE *yyvaluep)
{
  YY_USE (yyvaluep);
  if (!yymsg)
    yymsg = "Deleting";
  YY_SYMBOL_PRINT (yymsg, yykind, yyvaluep, yylocationp);

  YY_IGNORE_MAYBE_UNINITIALIZED_BEGIN
  YY_USE (yykind);
  YY_IGNORE_MAYBE_UNINITIALIZED_END
}


/* Lookahead token kind.  */
int yychar;

/* The semantic value of the lookahead symbol.  */
YYSTYPE yylval;
/* Number of syntax errors so far.  */
int yynerrs;




/*----------.
| yyparse.  |
`----------*/

int
yyparse (void)
{
    yy_state_fast_t yystate = 0;
    /* Number of tokens to shift before error messages enabled.  */
    int yyerrstatus = 0;

    /* Refer to the stacks through separate pointers, to allow yyoverflow
       to reallocate them elsewhere.  */

    /* Their size.  */
    YYPTRDIFF_T yystacksize = YYINITDEPTH;

    /* The state stack: array, bottom, top.  */
    yy_state_t yyssa[YYINITDEPTH];
    yy_state_t *yyss = yyssa;
    yy_state_t *yyssp = yyss;

    /* The semantic value stack: array, bottom, top.  */
    YYSTYPE yyvsa[YYINITDEPTH];
    YYSTYPE *yyvs = yyvsa;
    YYSTYPE *yyvsp = yyvs;

  int yyn;
  /* The return value of yyparse.  */
  int yyresult;
  /* Lookahead symbol kind.  */
  yysymbol_kind_t yytoken = YYSYMBOL_YYEMPTY;
  /* The variables used to return semantic value and location from the
     action routines.  */
  YYSTYPE yyval;



#define YYPOPSTACK(N)   (yyvsp -= (N), yyssp -= (N))

  /* The number of symbols on the RHS of the reduced rule.
     Keep to zero when no symbol should be popped.  */
  int yylen = 0;

  YYDPRINTF ((stderr, "Starting parse\n"));

  yychar = YYEMPTY; /* Cause a token to be read.  */

  goto yysetstate;


/*------------------------------------------------------------.
| yynewstate -- push a new state, which is found in yystate.  |
`------------------------------------------------------------*/
yynewstate:
  /* In all cases, when you get here, the value and location stacks
     have just been pushed.  So pushing a state here evens the stacks.  */
  yyssp++;


/*--------------------------------------------------------------------.
| yysetstate -- set current state (the top of the stack) to yystate.  |
`--------------------------------------------------------------------*/
yysetstate:
  YYDPRINTF ((stderr, "Entering state %d\n", yystate));
  YY_ASSERT (0 <= yystate && yystate < YYNSTATES);
  YY_IGNORE_USELESS_CAST_BEGIN
  *yyssp = YY_CAST (yy_state_t, yystate);
  YY_IGNORE_USELESS_CAST_END
  YY_STACK_PRINT (yyss, yyssp);

  if (yyss + yystacksize - 1 <= yyssp)
#if !defined yyoverflow && !defined YYSTACK_RELOCATE
    YYNOMEM;
#else
    {
      /* Get the current used size of the three stacks, in elements.  */
      YYPTRDIFF_T yysize = yyssp - yyss + 1;

# if defined yyoverflow
      {
        /* Give user a chance to reallocate the stack.  Use copies of
           these so that the &'s don't force the real ones into
           memory.  */
        yy_state_t *yyss1 = yyss;
        YYSTYPE *yyvs1 = yyvs;

        /* Each stack pointer address is followed by the size of the
           data in use in that stack, in bytes.  This used to be a
           conditional around just the two extra args, but that might
           be undefined if yyoverflow is a macro.  */
        yyoverflow (YY_("memory exhausted"),
                    &yyss1, yysize * YYSIZEOF (*yyssp),
                    &yyvs1, yysize * YYSIZEOF (*yyvsp),
                    &yystacksize);
        yyss = yyss1;
        yyvs = yyvs1;
      }
# else /* defined YYSTACK_RELOCATE */
      /* Extend the stack our own way.  */
      if (YYMAXDEPTH <= yystacksize)
        YYNOMEM;
      yystacksize *= 2;
      if (YYMAXDEPTH < yystacksize)
        yystacksize = YYMAXDEPTH;

      {
        yy_state_t *yyss1 = yyss;
        union yyalloc *yyptr =
          YY_CAST (union yyalloc *,
                   YYSTACK_ALLOC (YY_CAST (YYSIZE_T, YYSTACK_BYTES (yystacksize))));
        if (! yyptr)
          YYNOMEM;
        YYSTACK_RELOCATE (yyss_alloc, yyss);
        YYSTACK_RELOCATE (yyvs_alloc, yyvs);
#  undef YYSTACK_RELOCATE
        if (yyss1 != yyssa)
          YYSTACK_FREE (yyss1);
      }
# endif

      yyssp = yyss + yysize - 1;
      yyvsp = yyvs + yysize - 1;

      YY_IGNORE_USELESS_CAST_BEGIN
      YYDPRINTF ((stderr, "Stack size increased to %ld\n",
                  YY_CAST (long, yystacksize)));
      YY_IGNORE_USELESS_CAST_END

      if (yyss + yystacksize - 1 <= yyssp)
        YYABORT;
    }
#endif /* !defined yyoverflow && !defined YYSTACK_RELOCATE */


  if (yystate == YYFINAL)
    YYACCEPT;

  goto yybackup;


/*-----------.
| yybackup.  |
`-----------*/
yybackup:
  /* Do appropriate processing given the current state.  Read a
     lookahead token if we need one and don't already have one.  */

  /* First try to decide what to do without reference to lookahead token.  */
  yyn = yypact[yystate];
  if (yypact_value_is_default (yyn))
    goto yydefault;

  /* Not known => get a lookahead token if don't already have one.  */

  /* YYCHAR is either empty, or end-of-input, or a valid lookahead.  */
  if (yychar == YYEMPTY)
    {
      YYDPRINTF ((stderr, "Reading a token\n"));
      yychar = yylex ();
    }

  if (yychar <= YYEOF)
    {
      yychar = YYEOF;
      yytoken = YYSYMBOL_YYEOF;
      YYDPRINTF ((stderr, "Now at end of input.\n"));
    }
  else if (yychar == YYerror)
    {
      /* The scanner already issued an error message, process directly
         to error recovery.  But do not keep the error token as
         lookahead, it is too special and may lead us to an endless
         loop in error recovery. */
      yychar = YYUNDEF;
      yytoken = YYSYMBOL_YYerror;
      goto yyerrlab1;
    }
  else
    {
      yytoken = YYTRANSLATE (yychar);
      YY_SYMBOL_PRINT ("Next token is", yytoken, &yylval, &yylloc);
    }

  /* If the proper action on seeing token YYTOKEN is to reduce or to
     detect an error, take that action.  */
  yyn += yytoken;
  if (yyn < 0 || YYLAST < yyn || yycheck[yyn] != yytoken)
    goto yydefault;
  yyn = yytable[yyn];
  if (yyn <= 0)
    {
      if (yytable_value_is_error (yyn))
        goto yyerrlab;
      yyn = -yyn;
      goto yyreduce;
    }

  /* Count tokens shifted since error; after three, turn off error
     status.  */
  if (yyerrstatus)
    yyerrstatus--;

  /* Shift the lookahead token.  */
  YY_SYMBOL_PRINT ("Shifting", yytoken, &yylval, &yylloc);
  yystate = yyn;
  YY_IGNORE_MAYBE_UNINITIALIZED_BEGIN
  *++yyvsp = yylval;
  YY_IGNORE_MAYBE_UNINITIALIZED_END

  /* Discard the shifted token.  */
  yychar = YYEMPTY;
  goto yynewstate;


/*-----------------------------------------------------------.
| yydefault -- do the default action for the current state.  |
`-----------------------------------------------------------*/
yydefault:
  yyn = yydefact[yystate];
  if (yyn == 0)
    goto yyerrlab;
  goto yyreduce;


/*-----------------------------.
| yyreduce -- do a reduction.  |
`-----------------------------*/
yyreduce:
  /* yyn is the number of a rule to reduce with.  */
  yylen = yyr2[yyn];

  /* If YYLEN is nonzero, implement the default value of the action:
     '$$ = $1'.

     Otherwise, the following line sets YYVAL to garbage.
     This behavior is undocumented and Bison
     users should not rely upon it.  Assigning to YYVAL
     unconditionally makes the parser a bit smaller, and it avoids a
     GCC warning that YYVAL may be used uninitialized.  */
  yyval = yyvsp[1-yylen];


  YY_REDUCE_PRINT (yyn);
  switch (yyn)
    {
  case 2: /* CompileUnit: FuncDef  */
#line 75 "/home/<USER>/byyl/exp04-minic-expr/frontend/flexbison/MiniC.y"
                      {

		// 创建一个编译单元的节点AST_OP_COMPILE_UNIT
		(yyval.node) = create_contain_node(ast_operator_type::AST_OP_COMPILE_UNIT, (yyvsp[0].node));

		// 设置到全局变量中
		ast_root = (yyval.node);
	}
#line 1152 "/home/<USER>/byyl/exp04-minic-expr/frontend/flexbison/autogenerated/MiniCBison.cpp"
    break;

  case 3: /* CompileUnit: VarDecl  */
#line 83 "/home/<USER>/byyl/exp04-minic-expr/frontend/flexbison/MiniC.y"
                  {

		// 创建一个编译单元的节点AST_OP_COMPILE_UNIT
		(yyval.node) = create_contain_node(ast_operator_type::AST_OP_COMPILE_UNIT, (yyvsp[0].node));
		ast_root = (yyval.node);
	}
#line 1163 "/home/<USER>/byyl/exp04-minic-expr/frontend/flexbison/autogenerated/MiniCBison.cpp"
    break;

  case 4: /* CompileUnit: CompileUnit FuncDef  */
#line 89 "/home/<USER>/byyl/exp04-minic-expr/frontend/flexbison/MiniC.y"
                              {

		// 把函数定义的节点作为编译单元的孩子
		(yyval.node) = (yyvsp[-1].node)->insert_son_node((yyvsp[0].node));
	}
#line 1173 "/home/<USER>/byyl/exp04-minic-expr/frontend/flexbison/autogenerated/MiniCBison.cpp"
    break;

  case 5: /* CompileUnit: CompileUnit VarDecl  */
#line 94 "/home/<USER>/byyl/exp04-minic-expr/frontend/flexbison/MiniC.y"
                              {
		// 把变量定义的节点作为编译单元的孩子
		(yyval.node) = (yyvsp[-1].node)->insert_son_node((yyvsp[0].node));
	}
#line 1182 "/home/<USER>/byyl/exp04-minic-expr/frontend/flexbison/autogenerated/MiniCBison.cpp"
    break;

  case 6: /* FuncDef: BasicType T_ID T_L_PAREN T_R_PAREN Block  */
#line 101 "/home/<USER>/byyl/exp04-minic-expr/frontend/flexbison/MiniC.y"
                                                    {

		// 函数返回类型
		type_attr funcReturnType = (yyvsp[-4].type);

		// 函数名
		var_id_attr funcId = (yyvsp[-3].var_id);

		// 函数体节点即Block，即$5
		ast_node * blockNode = (yyvsp[0].node);

		// 形参结点没有，设置为空指针
		ast_node * formalParamsNode = nullptr;

		// 创建函数定义的节点，孩子有类型，函数名，语句块和形参(实际上无)
		// create_func_def函数内会释放funcId中指向的标识符空间，切记，之后不要再释放，之前一定要是通过strdup函数或者malloc分配的空间
		(yyval.node) = create_func_def(funcReturnType, funcId, blockNode, formalParamsNode);
	}
#line 1205 "/home/<USER>/byyl/exp04-minic-expr/frontend/flexbison/autogenerated/MiniCBison.cpp"
    break;

  case 7: /* Block: T_L_BRACE T_R_BRACE  */
#line 124 "/home/<USER>/byyl/exp04-minic-expr/frontend/flexbison/MiniC.y"
                            {
		// 语句块没有语句

		// 为了方便创建一个空的Block节点
		(yyval.node) = create_contain_node(ast_operator_type::AST_OP_BLOCK);
	}
#line 1216 "/home/<USER>/byyl/exp04-minic-expr/frontend/flexbison/autogenerated/MiniCBison.cpp"
    break;

  case 8: /* Block: T_L_BRACE BlockItemList T_R_BRACE  */
#line 130 "/home/<USER>/byyl/exp04-minic-expr/frontend/flexbison/MiniC.y"
                                            {
		// 语句块含有语句

		// BlockItemList归约时内部创建Block节点，并把语句加入，这里不创建Block节点
		(yyval.node) = (yyvsp[-1].node);
	}
#line 1227 "/home/<USER>/byyl/exp04-minic-expr/frontend/flexbison/autogenerated/MiniCBison.cpp"
    break;

  case 9: /* BlockItemList: BlockItem  */
#line 141 "/home/<USER>/byyl/exp04-minic-expr/frontend/flexbison/MiniC.y"
                          {
		// 第一个左侧的孩子节点归约成Block节点，后续语句可持续作为孩子追加到Block节点中
		// 创建一个AST_OP_BLOCK类型的中间节点，孩子为Statement($1)
		(yyval.node) = create_contain_node(ast_operator_type::AST_OP_BLOCK, (yyvsp[0].node));
	}
#line 1237 "/home/<USER>/byyl/exp04-minic-expr/frontend/flexbison/autogenerated/MiniCBison.cpp"
    break;

  case 10: /* BlockItemList: BlockItemList BlockItem  */
#line 146 "/home/<USER>/byyl/exp04-minic-expr/frontend/flexbison/MiniC.y"
                                  {
		// 把BlockItem归约的节点加入到BlockItemList的节点中
		(yyval.node) = (yyvsp[-1].node)->insert_son_node((yyvsp[0].node));
	}
#line 1246 "/home/<USER>/byyl/exp04-minic-expr/frontend/flexbison/autogenerated/MiniCBison.cpp"
    break;

  case 11: /* BlockItem: Statement  */
#line 155 "/home/<USER>/byyl/exp04-minic-expr/frontend/flexbison/MiniC.y"
                       {
		// 语句节点传递给归约后的节点上，综合属性
		(yyval.node) = (yyvsp[0].node);
	}
#line 1255 "/home/<USER>/byyl/exp04-minic-expr/frontend/flexbison/autogenerated/MiniCBison.cpp"
    break;

  case 12: /* BlockItem: VarDecl  */
#line 159 "/home/<USER>/byyl/exp04-minic-expr/frontend/flexbison/MiniC.y"
                  {
		// 变量声明节点传递给归约后的节点上，综合属性
		(yyval.node) = (yyvsp[0].node);
	}
#line 1264 "/home/<USER>/byyl/exp04-minic-expr/frontend/flexbison/autogenerated/MiniCBison.cpp"
    break;

  case 13: /* VarDecl: VarDeclExpr T_SEMICOLON  */
#line 170 "/home/<USER>/byyl/exp04-minic-expr/frontend/flexbison/MiniC.y"
                                  {
		(yyval.node) = (yyvsp[-1].node);
	}
#line 1272 "/home/<USER>/byyl/exp04-minic-expr/frontend/flexbison/autogenerated/MiniCBison.cpp"
    break;

  case 14: /* VarDeclExpr: BasicType VarDef  */
#line 176 "/home/<USER>/byyl/exp04-minic-expr/frontend/flexbison/MiniC.y"
                              {

		// 创建类型节点
		ast_node * type_node = create_type_node((yyvsp[-1].type));

		// 创建变量定义节点
		ast_node * decl_node = create_contain_node(ast_operator_type::AST_OP_VAR_DECL, type_node, (yyvsp[0].node));
		decl_node->type = type_node->type;

		// 创建变量声明语句，并加入第一个变量
		(yyval.node) = create_var_decl_stmt_node(decl_node);
	}
#line 1289 "/home/<USER>/byyl/exp04-minic-expr/frontend/flexbison/autogenerated/MiniCBison.cpp"
    break;

  case 15: /* VarDeclExpr: VarDeclExpr T_COMMA VarDef  */
#line 188 "/home/<USER>/byyl/exp04-minic-expr/frontend/flexbison/MiniC.y"
                                     {

		// 创建类型节点，这里从VarDeclExpr获取类型，前面已经设置
		ast_node * type_node = ast_node::New((yyvsp[-2].node)->type);

		// 创建变量定义节点
		ast_node * decl_node = create_contain_node(ast_operator_type::AST_OP_VAR_DECL, type_node, (yyvsp[0].node));

		// 插入到变量声明语句
		(yyval.node) = (yyvsp[-2].node)->insert_son_node(decl_node);
	}
#line 1305 "/home/<USER>/byyl/exp04-minic-expr/frontend/flexbison/autogenerated/MiniCBison.cpp"
    break;

  case 16: /* VarDef: T_ID  */
#line 202 "/home/<USER>/byyl/exp04-minic-expr/frontend/flexbison/MiniC.y"
              {
		// 变量ID

		(yyval.node) = ast_node::New(var_id_attr{(yyvsp[0].var_id).id, (yyvsp[0].var_id).lineno});

		// 对于字符型字面量的字符串空间需要释放，因词法用到了strdup进行了字符串复制
		free((yyvsp[0].var_id).id);
	}
#line 1318 "/home/<USER>/byyl/exp04-minic-expr/frontend/flexbison/autogenerated/MiniCBison.cpp"
    break;

  case 17: /* BasicType: T_INT  */
#line 213 "/home/<USER>/byyl/exp04-minic-expr/frontend/flexbison/MiniC.y"
                 {
		(yyval.type) = (yyvsp[0].type);
	}
#line 1326 "/home/<USER>/byyl/exp04-minic-expr/frontend/flexbison/autogenerated/MiniCBison.cpp"
    break;

  case 18: /* Statement: T_RETURN Expr T_SEMICOLON  */
#line 222 "/home/<USER>/byyl/exp04-minic-expr/frontend/flexbison/MiniC.y"
                                      {
		// 返回语句

		// 创建返回节点AST_OP_RETURN，其孩子为Expr，即$2
		(yyval.node) = create_contain_node(ast_operator_type::AST_OP_RETURN, (yyvsp[-1].node));
	}
#line 1337 "/home/<USER>/byyl/exp04-minic-expr/frontend/flexbison/autogenerated/MiniCBison.cpp"
    break;

  case 19: /* Statement: LVal T_ASSIGN Expr T_SEMICOLON  */
#line 228 "/home/<USER>/byyl/exp04-minic-expr/frontend/flexbison/MiniC.y"
                                         {
		// 赋值语句

		// 创建一个AST_OP_ASSIGN类型的中间节点，孩子为LVal($1)和Expr($3)
		(yyval.node) = create_contain_node(ast_operator_type::AST_OP_ASSIGN, (yyvsp[-3].node), (yyvsp[-1].node));
	}
#line 1348 "/home/<USER>/byyl/exp04-minic-expr/frontend/flexbison/autogenerated/MiniCBison.cpp"
    break;

  case 20: /* Statement: Block  */
#line 234 "/home/<USER>/byyl/exp04-minic-expr/frontend/flexbison/MiniC.y"
                {
		// 语句块

		// 内部已创建block节点，直接传递给Statement
		(yyval.node) = (yyvsp[0].node);
	}
#line 1359 "/home/<USER>/byyl/exp04-minic-expr/frontend/flexbison/autogenerated/MiniCBison.cpp"
    break;

  case 21: /* Statement: Expr T_SEMICOLON  */
#line 240 "/home/<USER>/byyl/exp04-minic-expr/frontend/flexbison/MiniC.y"
                           {
		// 表达式语句

		// 内部已创建表达式，直接传递给Statement
		(yyval.node) = (yyvsp[-1].node);
	}
#line 1370 "/home/<USER>/byyl/exp04-minic-expr/frontend/flexbison/autogenerated/MiniCBison.cpp"
    break;

  case 22: /* Statement: T_SEMICOLON  */
#line 246 "/home/<USER>/byyl/exp04-minic-expr/frontend/flexbison/MiniC.y"
                      {
		// 空语句

		// 直接返回空指针，需要再把语句加入到语句块时要注意判断，空语句不要加入
		(yyval.node) = nullptr;
	}
#line 1381 "/home/<USER>/byyl/exp04-minic-expr/frontend/flexbison/autogenerated/MiniCBison.cpp"
    break;

  case 23: /* Expr: AddExp  */
#line 256 "/home/<USER>/byyl/exp04-minic-expr/frontend/flexbison/MiniC.y"
              {
		// 直接传递给归约后的节点
		(yyval.node) = (yyvsp[0].node);
	}
#line 1390 "/home/<USER>/byyl/exp04-minic-expr/frontend/flexbison/autogenerated/MiniCBison.cpp"
    break;

  case 24: /* AddExp: UnaryExp  */
#line 266 "/home/<USER>/byyl/exp04-minic-expr/frontend/flexbison/MiniC.y"
                  {
		// 一目表达式

		// 直接传递到归约后的节点
		(yyval.node) = (yyvsp[0].node);
	}
#line 1401 "/home/<USER>/byyl/exp04-minic-expr/frontend/flexbison/autogenerated/MiniCBison.cpp"
    break;

  case 25: /* AddExp: UnaryExp AddOp UnaryExp  */
#line 272 "/home/<USER>/byyl/exp04-minic-expr/frontend/flexbison/MiniC.y"
                                  {
		// 两个一目表达式的加减运算

		// 创建加减运算节点，其孩子为两个一目表达式节点
		(yyval.node) = create_contain_node(ast_operator_type((yyvsp[-1].op_class)), (yyvsp[-2].node), (yyvsp[0].node));
	}
#line 1412 "/home/<USER>/byyl/exp04-minic-expr/frontend/flexbison/autogenerated/MiniCBison.cpp"
    break;

  case 26: /* AddExp: AddExp AddOp UnaryExp  */
#line 278 "/home/<USER>/byyl/exp04-minic-expr/frontend/flexbison/MiniC.y"
                                {
		// 左递归形式可通过加减连接多个一元表达式

		// 创建加减运算节点，孩子为AddExp($1)和UnaryExp($3)
		(yyval.node) = create_contain_node(ast_operator_type((yyvsp[-1].op_class)), (yyvsp[-2].node), (yyvsp[0].node));
	}
#line 1423 "/home/<USER>/byyl/exp04-minic-expr/frontend/flexbison/autogenerated/MiniCBison.cpp"
    break;

  case 27: /* AddOp: T_ADD  */
#line 287 "/home/<USER>/byyl/exp04-minic-expr/frontend/flexbison/MiniC.y"
             {
		(yyval.op_class) = (int)ast_operator_type::AST_OP_ADD;
	}
#line 1431 "/home/<USER>/byyl/exp04-minic-expr/frontend/flexbison/autogenerated/MiniCBison.cpp"
    break;

  case 28: /* AddOp: T_SUB  */
#line 290 "/home/<USER>/byyl/exp04-minic-expr/frontend/flexbison/MiniC.y"
                {
		(yyval.op_class) = (int)ast_operator_type::AST_OP_SUB;
	}
#line 1439 "/home/<USER>/byyl/exp04-minic-expr/frontend/flexbison/autogenerated/MiniCBison.cpp"
    break;

  case 29: /* UnaryExp: PrimaryExp  */
#line 299 "/home/<USER>/byyl/exp04-minic-expr/frontend/flexbison/MiniC.y"
                      {
		// 基本表达式

		// 传递到归约后的UnaryExp上
		(yyval.node) = (yyvsp[0].node);
	}
#line 1450 "/home/<USER>/byyl/exp04-minic-expr/frontend/flexbison/autogenerated/MiniCBison.cpp"
    break;

  case 30: /* UnaryExp: T_ID T_L_PAREN T_R_PAREN  */
#line 305 "/home/<USER>/byyl/exp04-minic-expr/frontend/flexbison/MiniC.y"
                                   {
		// 没有实参的函数调用

		// 创建函数调用名终结符节点
		ast_node * name_node = ast_node::New(std::string((yyvsp[-2].var_id).id), (yyvsp[-2].var_id).lineno);

		// 对于字符型字面量的字符串空间需要释放，因词法用到了strdup进行了字符串复制
		free((yyvsp[-2].var_id).id);

		// 实参列表
		ast_node * paramListNode = nullptr;

		// 创建函数调用节点，其孩子为被调用函数名和实参，实参为空，但函数内部会创建实参列表节点，无孩子
		(yyval.node) = create_func_call(name_node, paramListNode);

	}
#line 1471 "/home/<USER>/byyl/exp04-minic-expr/frontend/flexbison/autogenerated/MiniCBison.cpp"
    break;

  case 31: /* UnaryExp: T_ID T_L_PAREN RealParamList T_R_PAREN  */
#line 321 "/home/<USER>/byyl/exp04-minic-expr/frontend/flexbison/MiniC.y"
                                                 {
		// 含有实参的函数调用

		// 创建函数调用名终结符节点
		ast_node * name_node = ast_node::New(std::string((yyvsp[-3].var_id).id), (yyvsp[-3].var_id).lineno);

		// 对于字符型字面量的字符串空间需要释放，因词法用到了strdup进行了字符串复制
		free((yyvsp[-3].var_id).id);

		// 实参列表
		ast_node * paramListNode = (yyvsp[-1].node);

		// 创建函数调用节点，其孩子为被调用函数名和实参，实参不为空
		(yyval.node) = create_func_call(name_node, paramListNode);
	}
#line 1491 "/home/<USER>/byyl/exp04-minic-expr/frontend/flexbison/autogenerated/MiniCBison.cpp"
    break;

  case 32: /* PrimaryExp: T_L_PAREN Expr T_R_PAREN  */
#line 340 "/home/<USER>/byyl/exp04-minic-expr/frontend/flexbison/MiniC.y"
                                       {
		// 带有括号的表达式
		(yyval.node) = (yyvsp[-1].node);
	}
#line 1500 "/home/<USER>/byyl/exp04-minic-expr/frontend/flexbison/autogenerated/MiniCBison.cpp"
    break;

  case 33: /* PrimaryExp: T_DIGIT  */
#line 344 "/home/<USER>/byyl/exp04-minic-expr/frontend/flexbison/MiniC.y"
                  {
        	// 无符号整型字面量

		// 创建一个无符号整型的终结符节点
		(yyval.node) = ast_node::New((yyvsp[0].integer_num));
	}
#line 1511 "/home/<USER>/byyl/exp04-minic-expr/frontend/flexbison/autogenerated/MiniCBison.cpp"
    break;

  case 34: /* PrimaryExp: LVal  */
#line 350 "/home/<USER>/byyl/exp04-minic-expr/frontend/flexbison/MiniC.y"
                {
		// 具有左值的表达式

		// 直接传递到归约后的非终结符号PrimaryExp
		(yyval.node) = (yyvsp[0].node);
	}
#line 1522 "/home/<USER>/byyl/exp04-minic-expr/frontend/flexbison/autogenerated/MiniCBison.cpp"
    break;

  case 35: /* RealParamList: Expr  */
#line 362 "/home/<USER>/byyl/exp04-minic-expr/frontend/flexbison/MiniC.y"
                     {
		// 创建实参列表节点，并把当前的Expr节点加入
		(yyval.node) = create_contain_node(ast_operator_type::AST_OP_FUNC_REAL_PARAMS, (yyvsp[0].node));
	}
#line 1531 "/home/<USER>/byyl/exp04-minic-expr/frontend/flexbison/autogenerated/MiniCBison.cpp"
    break;

  case 36: /* RealParamList: RealParamList T_COMMA Expr  */
#line 366 "/home/<USER>/byyl/exp04-minic-expr/frontend/flexbison/MiniC.y"
                                     {
		// 左递归增加实参表达式
		(yyval.node) = (yyvsp[-2].node)->insert_son_node((yyvsp[0].node));
	}
#line 1540 "/home/<USER>/byyl/exp04-minic-expr/frontend/flexbison/autogenerated/MiniCBison.cpp"
    break;

  case 37: /* LVal: T_ID  */
#line 373 "/home/<USER>/byyl/exp04-minic-expr/frontend/flexbison/MiniC.y"
            {
		// 变量名终结符

		// 创建变量名终结符节点
		(yyval.node) = ast_node::New((yyvsp[0].var_id));

		// 对于字符型字面量的字符串空间需要释放，因词法用到了strdup进行了字符串复制
		free((yyvsp[0].var_id).id);
	}
#line 1554 "/home/<USER>/byyl/exp04-minic-expr/frontend/flexbison/autogenerated/MiniCBison.cpp"
    break;


#line 1558 "/home/<USER>/byyl/exp04-minic-expr/frontend/flexbison/autogenerated/MiniCBison.cpp"

      default: break;
    }
  /* User semantic actions sometimes alter yychar, and that requires
     that yytoken be updated with the new translation.  We take the
     approach of translating immediately before every use of yytoken.
     One alternative is translating here after every semantic action,
     but that translation would be missed if the semantic action invokes
     YYABORT, YYACCEPT, or YYERROR immediately after altering yychar or
     if it invokes YYBACKUP.  In the case of YYABORT or YYACCEPT, an
     incorrect destructor might then be invoked immediately.  In the
     case of YYERROR or YYBACKUP, subsequent parser actions might lead
     to an incorrect destructor call or verbose syntax error message
     before the lookahead is translated.  */
  YY_SYMBOL_PRINT ("-> $$ =", YY_CAST (yysymbol_kind_t, yyr1[yyn]), &yyval, &yyloc);

  YYPOPSTACK (yylen);
  yylen = 0;

  *++yyvsp = yyval;

  /* Now 'shift' the result of the reduction.  Determine what state
     that goes to, based on the state we popped back to and the rule
     number reduced by.  */
  {
    const int yylhs = yyr1[yyn] - YYNTOKENS;
    const int yyi = yypgoto[yylhs] + *yyssp;
    yystate = (0 <= yyi && yyi <= YYLAST && yycheck[yyi] == *yyssp
               ? yytable[yyi]
               : yydefgoto[yylhs]);
  }

  goto yynewstate;


/*--------------------------------------.
| yyerrlab -- here on detecting error.  |
`--------------------------------------*/
yyerrlab:
  /* Make sure we have latest lookahead translation.  See comments at
     user semantic actions for why this is necessary.  */
  yytoken = yychar == YYEMPTY ? YYSYMBOL_YYEMPTY : YYTRANSLATE (yychar);
  /* If not already recovering from an error, report this error.  */
  if (!yyerrstatus)
    {
      ++yynerrs;
      yyerror (YY_("syntax error"));
    }

  if (yyerrstatus == 3)
    {
      /* If just tried and failed to reuse lookahead token after an
         error, discard it.  */

      if (yychar <= YYEOF)
        {
          /* Return failure if at end of input.  */
          if (yychar == YYEOF)
            YYABORT;
        }
      else
        {
          yydestruct ("Error: discarding",
                      yytoken, &yylval);
          yychar = YYEMPTY;
        }
    }

  /* Else will try to reuse lookahead token after shifting the error
     token.  */
  goto yyerrlab1;


/*---------------------------------------------------.
| yyerrorlab -- error raised explicitly by YYERROR.  |
`---------------------------------------------------*/
yyerrorlab:
  /* Pacify compilers when the user code never invokes YYERROR and the
     label yyerrorlab therefore never appears in user code.  */
  if (0)
    YYERROR;
  ++yynerrs;

  /* Do not reclaim the symbols of the rule whose action triggered
     this YYERROR.  */
  YYPOPSTACK (yylen);
  yylen = 0;
  YY_STACK_PRINT (yyss, yyssp);
  yystate = *yyssp;
  goto yyerrlab1;


/*-------------------------------------------------------------.
| yyerrlab1 -- common code for both syntax error and YYERROR.  |
`-------------------------------------------------------------*/
yyerrlab1:
  yyerrstatus = 3;      /* Each real token shifted decrements this.  */

  /* Pop stack until we find a state that shifts the error token.  */
  for (;;)
    {
      yyn = yypact[yystate];
      if (!yypact_value_is_default (yyn))
        {
          yyn += YYSYMBOL_YYerror;
          if (0 <= yyn && yyn <= YYLAST && yycheck[yyn] == YYSYMBOL_YYerror)
            {
              yyn = yytable[yyn];
              if (0 < yyn)
                break;
            }
        }

      /* Pop the current state because it cannot handle the error token.  */
      if (yyssp == yyss)
        YYABORT;


      yydestruct ("Error: popping",
                  YY_ACCESSING_SYMBOL (yystate), yyvsp);
      YYPOPSTACK (1);
      yystate = *yyssp;
      YY_STACK_PRINT (yyss, yyssp);
    }

  YY_IGNORE_MAYBE_UNINITIALIZED_BEGIN
  *++yyvsp = yylval;
  YY_IGNORE_MAYBE_UNINITIALIZED_END


  /* Shift the error token.  */
  YY_SYMBOL_PRINT ("Shifting", YY_ACCESSING_SYMBOL (yyn), yyvsp, yylsp);

  yystate = yyn;
  goto yynewstate;


/*-------------------------------------.
| yyacceptlab -- YYACCEPT comes here.  |
`-------------------------------------*/
yyacceptlab:
  yyresult = 0;
  goto yyreturnlab;


/*-----------------------------------.
| yyabortlab -- YYABORT comes here.  |
`-----------------------------------*/
yyabortlab:
  yyresult = 1;
  goto yyreturnlab;


/*-----------------------------------------------------------.
| yyexhaustedlab -- YYNOMEM (memory exhaustion) comes here.  |
`-----------------------------------------------------------*/
yyexhaustedlab:
  yyerror (YY_("memory exhausted"));
  yyresult = 2;
  goto yyreturnlab;


/*----------------------------------------------------------.
| yyreturnlab -- parsing is finished, clean up and return.  |
`----------------------------------------------------------*/
yyreturnlab:
  if (yychar != YYEMPTY)
    {
      /* Make sure we have latest lookahead translation.  See comments at
         user semantic actions for why this is necessary.  */
      yytoken = YYTRANSLATE (yychar);
      yydestruct ("Cleanup: discarding lookahead",
                  yytoken, &yylval);
    }
  /* Do not reclaim the symbols of the rule whose action triggered
     this YYABORT or YYACCEPT.  */
  YYPOPSTACK (yylen);
  YY_STACK_PRINT (yyss, yyssp);
  while (yyssp != yyss)
    {
      yydestruct ("Cleanup: popping",
                  YY_ACCESSING_SYMBOL (+*yyssp), yyvsp);
      YYPOPSTACK (1);
    }
#ifndef yyoverflow
  if (yyss != yyssa)
    YYSTACK_FREE (yyss);
#endif

  return yyresult;
}

#line 384 "/home/<USER>/byyl/exp04-minic-expr/frontend/flexbison/MiniC.y"


// 语法识别错误要调用函数的定义
void yyerror(char * msg)
{
    printf("Line %d: %s\n", yylineno, msg);
}
