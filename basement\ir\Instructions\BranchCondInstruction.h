///
/// @file BranchCondInstruction.h
/// @brief 有条件分支指令（根据比较结果跳转到真或假分支）
///
#pragma once

#include <string>
#include "Instruction.h"
#include "LabelInstruction.h"
#include "Function.h"

///
/// @brief 有条件跳转指令
///        操作码现在分为 IRINST_OP_BC_LT, _GT, _LE, _GE, _EQ, _NE
///
class BranchCondInstruction final : public Instruction {
public:
    ///
    /// @brief 构造函数
    /// @param _func        当前函数
    /// @param _op          操作码，必须是 IRINST_OP_BC_* 中的一种
    /// @param _condValue   比较产生的 i32 布尔值（0 或 1）
    /// @param _trueLabel   为真时跳转的标签
    /// @param _falseLabel  为假时跳转的标签
    ///
    BranchCondInstruction(Function * _func,
                          IRInstOperator _op,
                          Value * _condValue,
                          LabelInstruction * _trueLabel,
                          LabelInstruction * _falseLabel);

    /// @brief 转换成 IR 文本
    void toString(std::string & str) override;

    [[nodiscard]] LabelInstruction * getTrueLabel() const;
    [[nodiscard]] LabelInstruction * getFalseLabel() const;

private:
    Value * condValue;
    LabelInstruction * trueLabel;
    LabelInstruction * falseLabel;
};
