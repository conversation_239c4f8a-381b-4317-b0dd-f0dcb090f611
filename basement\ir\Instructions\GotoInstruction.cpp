///
/// @file GotoInstruction.cpp
/// @brief 无条件跳转指令即goto指令
///
/// <AUTHOR> (<EMAIL>)
/// @version 1.0
/// @date 2024-09-29
///
/// @copyright Copyright (c) 2024
///
/// @par 修改日志:
/// <table>
/// <tr><th>Date       <th>Version <th>Author  <th>Description
/// <tr><td>2024-09-29 <td>1.0     <td>zenglj  <td>新建
/// </table>
///

#include "VoidType.h"

#include "GotoInstruction.h"

///
/// @brief 无条件跳转指令的构造函数
/// @param target 跳转目标
///
// 通用构造函数（参数名保持与原有代码一致）
GotoInstruction::GotoInstruction(Function * _func, Instruction * _target)
    : GotoInstruction(_func, dynamic_cast<LabelInstruction *>(_target)) // 委托构造
{}

// 专用构造函数（新增）
GotoInstruction::GotoInstruction(Function * _func, LabelInstruction * targetLabel)
    : Instruction(_func, IRInstOperator::IRINST_OP_GOTO, VoidType::getType()), target(targetLabel)
{
    // 将Label指令添加为操作数
    addOperand(targetLabel);
}

/// @brief 转换成IR指令文本
void GotoInstruction::toString(std::string & str)
{
    str = "br label " + target->getIRName();
}

///
/// @brief 获取目标Label指令
/// @return LabelInstruction* label指令
///
LabelInstruction * GotoInstruction::getTarget() const
{
    return target;
}
